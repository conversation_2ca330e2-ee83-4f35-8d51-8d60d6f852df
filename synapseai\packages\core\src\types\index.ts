import { z } from 'zod';

// Base Entity Types
export const BaseEntitySchema = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  tenantId: z.string(),
});

export type BaseEntity = z.infer<typeof BaseEntitySchema>;

// User & Authentication
export const UserRoleSchema = z.enum([
  'ADMIN',
  'OWNER',
  'MEMBER',
  'VIEWER',
  'GUEST',
]);

export const UserSchema = BaseEntitySchema.extend({
  email: z.string().email(),
  name: z.string().min(1),
  avatar: z.string().url().optional(),
  role: UserRoleSchema,
  isActive: z.boolean().default(true),
  lastLoginAt: z.date().optional(),
  preferences: z.record(z.unknown()).default({}),
});

export type User = z.infer<typeof UserSchema>;
export type UserRole = z.infer<typeof UserRoleSchema>;

// Tenant & Multi-tenancy
export const TenantSchema = BaseEntitySchema.extend({
  name: z.string().min(1),
  slug: z.string().min(1),
  description: z.string().optional(),
  settings: z.record(z.unknown()).default({}),
  quotas: z.object({
    maxUsers: z.number().positive(),
    maxAgents: z.number().positive(),
    maxWorkflows: z.number().positive(),
    maxAPICallsPerMonth: z.number().positive(),
    storageGB: z.number().positive(),
  }),
  isActive: z.boolean().default(true),
});

export type Tenant = z.infer<typeof TenantSchema>;

// Session & Memory
export const SessionSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
  userId: z.string().optional(),
  type: z.enum(['AGENT', 'WORKFLOW', 'TOOL', 'HYBRID']),
  status: z.enum(['ACTIVE', 'PAUSED', 'COMPLETED', 'FAILED', 'CANCELLED']),
  context: z.record(z.unknown()).default({}),
  memory: z.record(z.unknown()).default({}),
  metadata: z.record(z.unknown()).default({}),
  startedAt: z.date(),
  endedAt: z.date().optional(),
  lastActivityAt: z.date(),
});

export type Session = z.infer<typeof SessionSchema>;

// Provider Configuration
export const ProviderTypeSchema = z.enum([
  'OPENAI',
  'ANTHROPIC',
  'GOOGLE',
  'MISTRAL',
  'GROQ',
  'DEEPSEEK',
  'HUGGINGFACE',
  'OLLAMA',
  'LOCALAI',
]);

export const ModelCapabilitySchema = z.enum([
  'CHAT',
  'EMBEDDING',
  'VISION',
  'FUNCTION_CALL',
  'CODE_GENERATION',
  'IMAGE_GENERATION',
  'AUDIO_GENERATION',
]);

export const ProviderConfigSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
  type: ProviderTypeSchema,
  name: z.string(),
  apiKey: z.string(),
  baseUrl: z.string().url().optional(),
  isEnabled: z.boolean().default(true),
  models: z.array(z.object({
    id: z.string(),
    name: z.string(),
    capabilities: z.array(ModelCapabilitySchema),
    maxTokens: z.number().positive(),
    costPer1KTokens: z.number().nonnegative(),
    rateLimits: z.object({
      requestsPerMinute: z.number().positive(),
      tokensPerMinute: z.number().positive(),
    }),
  })),
  settings: z.record(z.unknown()).default({}),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type ProviderConfig = z.infer<typeof ProviderConfigSchema>;
export type ProviderType = z.infer<typeof ProviderTypeSchema>;
export type ModelCapability = z.infer<typeof ModelCapabilitySchema>;

// Execution Status & Results
export const ExecutionStatusSchema = z.enum([
  'PENDING',
  'RUNNING',
  'COMPLETED',
  'FAILED',
  'CANCELLED',
  'TIMEOUT',
]);

export const ExecutionResultSchema = z.object({
  id: z.string(),
  status: ExecutionStatusSchema,
  result: z.unknown().optional(),
  error: z.string().optional(),
  metadata: z.record(z.unknown()).default({}),
  startedAt: z.date(),
  completedAt: z.date().optional(),
  duration: z.number().nonnegative().optional(),
});

export type ExecutionStatus = z.infer<typeof ExecutionStatusSchema>;
export type ExecutionResult = z.infer<typeof ExecutionResultSchema>;

// Audit & Logging
export const LogLevelSchema = z.enum([
  'DEBUG',
  'INFO',
  'WARN',
  'ERROR',
  'FATAL',
]);

export const AuditLogSchema = BaseEntitySchema.extend({
  action: z.string(),
  resourceType: z.string(),
  resourceId: z.string(),
  userId: z.string().optional(),
  sessionId: z.string().optional(),
  details: z.record(z.unknown()).default({}),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  level: LogLevelSchema,
});

export type AuditLog = z.infer<typeof AuditLogSchema>;
export type LogLevel = z.infer<typeof LogLevelSchema>;

// API & Validation
export const PaginationSchema = z.object({
  page: z.number().positive().default(1),
  limit: z.number().positive().max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    items: z.array(itemSchema),
    total: z.number().nonnegative(),
    page: z.number().positive(),
    limit: z.number().positive(),
    totalPages: z.number().nonnegative(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  });

export type Pagination = z.infer<typeof PaginationSchema>;
export type PaginatedResponse<T> = {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
};

// Error Types
export const ErrorCodeSchema = z.enum([
  'VALIDATION_ERROR',
  'AUTHENTICATION_ERROR',
  'AUTHORIZATION_ERROR',
  'NOT_FOUND_ERROR',
  'CONFLICT_ERROR',
  'RATE_LIMIT_ERROR',
  'PROVIDER_ERROR',
  'EXECUTION_ERROR',
  'NETWORK_ERROR',
  'INTERNAL_ERROR',
]);

export const ApiErrorSchema = z.object({
  code: ErrorCodeSchema,
  message: z.string(),
  details: z.record(z.unknown()).optional(),
  timestamp: z.date(),
  requestId: z.string(),
  path: z.string().optional(),
});

export type ErrorCode = z.infer<typeof ErrorCodeSchema>;
export type ApiError = z.infer<typeof ApiErrorSchema>;
