# Dependencies
node_modules/
.pnpm-store/

# Build outputs
dist/
build/
.next/
out/
coverage/
.turbo/

# Environment files
.env*

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Generated files
generated/
*.generated.*
*.tsbuildinfo

# Package files
*.tgz
*.tar.gz

# Prisma
prisma/migrations/
prisma/generated/

# Database
*.db
*.sqlite

# Cache
.cache/
.parcel-cache/
.eslintcache
