{"name": "@synapseai/sdk", "version": "0.1.0", "type": "module", "description": "SynapseAI SDK - Multi-language Client Libraries", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./client": {"types": "./dist/client/index.d.ts", "import": "./dist/client/index.js"}, "./react": {"types": "./dist/react/index.d.ts", "import": "./dist/react/index.js"}, "./vue": {"types": "./dist/vue/index.d.ts", "import": "./dist/vue/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && tsc-alias", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@synapseai/core": "workspace:*", "ws": "^8.18.0", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.8.4", "@types/react": "^18.3.12", "@types/ws": "^8.5.13", "react": "^18.3.1", "tsc-alias": "^1.8.10", "typescript": "^5.6.3", "vitest": "^2.1.9", "vue": "^3.5.13"}, "peerDependencies": {"react": ">=16.8.0", "vue": ">=3.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "vue": {"optional": true}}}