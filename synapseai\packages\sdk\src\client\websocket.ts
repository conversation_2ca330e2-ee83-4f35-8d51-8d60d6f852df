import { EventEmitter } from 'events';
import { WebSocketConnection, WebSocketMessage } from '../types';

declare global {
  interface WebSocket {
    new (url: string, protocols?: string | string[]): WebSocket;
  }
}

export class WebSocketManager extends EventEmitter {
  private connection: WebSocketConnection;
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private subscriptions = new Map<string, Set<(message: WebSocketMessage) => void>>();
  private messageQueue: WebSocketMessage[] = [];
  private isConnecting = false;

  constructor(connection: WebSocketConnection) {
    super();
    this.connection = {
      reconnect: true,
      maxReconnects: 5,
      reconnectInterval: 1000,
      ...connection,
    };
  }

  public connect(): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
      return;
    }

    this.isConnecting = true;
    
    try {
      // Determine WebSocket constructor based on environment
      const WebSocketClass = this.getWebSocketClass();
      
      this.ws = new WebSocketClass(
        this.connection.url,
        this.connection.protocols
      );

      this.setupEventHandlers();
    } catch (error) {
      this.isConnecting = false;
      this.emit('error', error);
    }
  }

  public disconnect(): void {
    this.connection.reconnect = false;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  public send(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Queue message for later sending
      this.messageQueue.push(message);
    }
  }

  public subscribe(channel: string, callback: (message: WebSocketMessage) => void): void {
    if (!this.subscriptions.has(channel)) {
      this.subscriptions.set(channel, new Set());
    }
    this.subscriptions.get(channel)!.add(callback);
  }

  public unsubscribe(channel: string, callback?: (message: WebSocketMessage) => void): void {
    const channelSubscriptions = this.subscriptions.get(channel);
    if (!channelSubscriptions) return;

    if (callback) {
      channelSubscriptions.delete(callback);
      if (channelSubscriptions.size === 0) {
        this.subscriptions.delete(channel);
      }
    } else {
      this.subscriptions.delete(channel);
    }
  }

  public getReadyState(): number {
    return this.ws?.readyState ?? WebSocket.CLOSED;
  }

  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.emit('connect');
      this.processMessageQueue();
    };

    this.ws.onclose = (event) => {
      this.isConnecting = false;
      this.emit('disconnect', event);
      
      if (this.connection.reconnect && this.reconnectAttempts < this.connection.maxReconnects) {
        this.scheduleReconnect();
      } else if (this.reconnectAttempts >= this.connection.maxReconnects) {
        this.emit('max_reconnects');
      }
    };

    this.ws.onerror = (error) => {
      this.isConnecting = false;
      this.emit('error', error);
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        this.emit('error', new Error(`Failed to parse WebSocket message: ${error}`));
      }
    };
  }

  private handleMessage(message: WebSocketMessage): void {
    this.emit('message', message);
    
    // Notify channel subscribers
    const channelSubscriptions = this.subscriptions.get(message.channel);
    if (channelSubscriptions) {
      channelSubscriptions.forEach(callback => {
        try {
          callback(message);
        } catch (error) {
          this.emit('error', new Error(`Error in subscription callback: ${error}`));
        }
      });
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.emit('reconnecting');
    
    const delay = this.connection.reconnectInterval * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    this.reconnectTimer = setTimeout(() => {
      this.emit('reconnect');
      this.connect();
    }, delay);
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift();
      if (message) {
        this.send(message);
      }
    }
  }

  private getWebSocketClass(): typeof WebSocket {
    // Browser environment
    if (typeof window !== 'undefined' && window.WebSocket) {
      return window.WebSocket;
    }
    
    // Node.js environment
    if (typeof global !== 'undefined') {
      try {
        // Try to import ws library
        const WebSocket = require('ws');
        return WebSocket;
      } catch (error) {
        throw new Error('WebSocket implementation not found. Install "ws" package for Node.js support.');
      }
    }
    
    throw new Error('WebSocket implementation not available');
  }
}
