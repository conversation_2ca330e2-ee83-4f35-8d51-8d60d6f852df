import { z } from 'zod';
import { 
  AgentFactory, 
  AgentConfig, 
  AgentConfigSchema, 
  Agent, 
  AgentType, 
  AgentCapability 
} from '../types';
import { BaseAgent } from '../runtime/baseAgent';

// Conversational Agent Factory
export class ConversationalAgentFactory implements AgentFactory {
  async create(config: AgentConfig): Promise<Agent> {
    const validatedConfig = this.validate(config);
    return new BaseAgent(validatedConfig);
  }

  validate(config: unknown): AgentConfig {
    return AgentConfigSchema.parse({
      ...config,
      type: 'CONVERSATIONAL',
      capabilities: ['CHAT', 'MEMORY_MANAGEMENT']
    });
  }

  getDefaultConfig(): Partial<AgentConfig> {
    return {
      type: 'CONVERSATIONAL',
      capabilities: ['CHAT', 'MEMORY_MANAGEMENT'],
      provider: 'OPENAI',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 4000,
      memory: {
        enabled: true,
        maxSize: 10000,
        strategy: 'FIFO',
        persistence: true,
      },
      isActive: true,
    };
  }

  getSupportedCapabilities(): AgentCapability[] {
    return ['CHAT', 'MEMORY_MANAGEMENT', 'REAL_TIME'];
  }
}

// Tool-Driven Agent Factory
export class ToolDrivenAgentFactory implements AgentFactory {
  async create(config: AgentConfig): Promise<Agent> {
    const validatedConfig = this.validate(config);
    return new BaseAgent(validatedConfig);
  }

  validate(config: unknown): AgentConfig {
    return AgentConfigSchema.parse({
      ...config,
      type: 'TOOL_DRIVEN',
      capabilities: ['TOOL_EXECUTION', 'FUNCTION_CALLING']
    });
  }

  getDefaultConfig(): Partial<AgentConfig> {
    return {
      type: 'TOOL_DRIVEN',
      capabilities: ['TOOL_EXECUTION', 'FUNCTION_CALLING'],
      provider: 'OPENAI',
      model: 'gpt-4',
      temperature: 0.3,
      maxTokens: 4000,
      memory: {
        enabled: true,
        maxSize: 5000,
        strategy: 'PRIORITY',
        persistence: true,
      },
      isActive: true,
    };
  }

  getSupportedCapabilities(): AgentCapability[] {
    return ['TOOL_EXECUTION', 'FUNCTION_CALLING', 'WORKFLOW_ORCHESTRATION'];
  }
}

// Hybrid Agent Factory
export class HybridAgentFactory implements AgentFactory {
  async create(config: AgentConfig): Promise<Agent> {
    const validatedConfig = this.validate(config);
    return new BaseAgent(validatedConfig);
  }

  validate(config: unknown): AgentConfig {
    return AgentConfigSchema.parse({
      ...config,
      type: 'HYBRID',
      capabilities: ['CHAT', 'TOOL_EXECUTION', 'WORKFLOW_ORCHESTRATION']
    });
  }

  getDefaultConfig(): Partial<AgentConfig> {
    return {
      type: 'HYBRID',
      capabilities: ['CHAT', 'TOOL_EXECUTION', 'WORKFLOW_ORCHESTRATION'],
      provider: 'OPENAI',
      model: 'gpt-4',
      temperature: 0.5,
      maxTokens: 8000,
      memory: {
        enabled: true,
        maxSize: 15000,
        strategy: 'PRIORITY',
        persistence: true,
      },
      isActive: true,
    };
  }

  getSupportedCapabilities(): AgentCapability[] {
    return [
      'CHAT',
      'TOOL_EXECUTION',
      'WORKFLOW_ORCHESTRATION',
      'MEMORY_MANAGEMENT',
      'FUNCTION_CALLING',
      'REAL_TIME'
    ];
  }
}

// Multi-Modal Agent Factory
export class MultiModalAgentFactory implements AgentFactory {
  async create(config: AgentConfig): Promise<Agent> {
    const validatedConfig = this.validate(config);
    return new BaseAgent(validatedConfig);
  }

  validate(config: unknown): AgentConfig {
    return AgentConfigSchema.parse({
      ...config,
      type: 'MULTI_TASKING',
      capabilities: ['MULTI_MODAL', 'CHAT', 'CODE_GENERATION']
    });
  }

  getDefaultConfig(): Partial<AgentConfig> {
    return {
      type: 'MULTI_TASKING',
      capabilities: ['MULTI_MODAL', 'CHAT', 'CODE_GENERATION'],
      provider: 'OPENAI',
      model: 'gpt-4-vision-preview',
      temperature: 0.7,
      maxTokens: 4000,
      memory: {
        enabled: true,
        maxSize: 20000,
        strategy: 'PRIORITY',
        persistence: true,
      },
      isActive: true,
    };
  }

  getSupportedCapabilities(): AgentCapability[] {
    return [
      'MULTI_MODAL',
      'CHAT',
      'CODE_GENERATION',
      'MEMORY_MANAGEMENT',
      'REAL_TIME'
    ];
  }
}

// RAG Agent Factory
export class RAGAgentFactory implements AgentFactory {
  async create(config: AgentConfig): Promise<Agent> {
    const validatedConfig = this.validate(config);
    return new BaseAgent(validatedConfig);
  }

  validate(config: unknown): AgentConfig {
    return AgentConfigSchema.parse({
      ...config,
      type: 'AUTONOMOUS',
      capabilities: ['RAG', 'CHAT', 'MEMORY_MANAGEMENT']
    });
  }

  getDefaultConfig(): Partial<AgentConfig> {
    return {
      type: 'AUTONOMOUS',
      capabilities: ['RAG', 'CHAT', 'MEMORY_MANAGEMENT'],
      provider: 'OPENAI',
      model: 'gpt-4',
      temperature: 0.4,
      maxTokens: 6000,
      memory: {
        enabled: true,
        maxSize: 25000,
        strategy: 'PRIORITY',
        persistence: true,
      },
      isActive: true,
    };
  }

  getSupportedCapabilities(): AgentCapability[] {
    return [
      'RAG',
      'CHAT',
      'MEMORY_MANAGEMENT',
      'TOOL_EXECUTION',
      'REAL_TIME'
    ];
  }
}

// Factory Registry
export class AgentFactoryRegistry {
  private factories = new Map<AgentType, AgentFactory>();

  constructor() {
    // Register default factories
    this.register('CONVERSATIONAL', new ConversationalAgentFactory());
    this.register('TOOL_DRIVEN', new ToolDrivenAgentFactory());
    this.register('HYBRID', new HybridAgentFactory());
    this.register('MULTI_TASKING', new MultiModalAgentFactory());
    this.register('AUTONOMOUS', new RAGAgentFactory());
  }

  register(type: AgentType, factory: AgentFactory): void {
    this.factories.set(type, factory);
  }

  unregister(type: AgentType): void {
    this.factories.delete(type);
  }

  get(type: AgentType): AgentFactory | null {
    return this.factories.get(type) || null;
  }

  list(): AgentType[] {
    return Array.from(this.factories.keys());
  }

  async createAgent(type: AgentType, config: Partial<AgentConfig>): Promise<Agent> {
    const factory = this.get(type);
    if (!factory) {
      throw new Error(`No factory registered for agent type: ${type}`);
    }

    const defaultConfig = factory.getDefaultConfig();
    const fullConfig = { ...defaultConfig, ...config, type };
    
    return factory.create(factory.validate(fullConfig));
  }
}

// Default factory registry instance
export const defaultAgentFactoryRegistry = new AgentFactoryRegistry();
