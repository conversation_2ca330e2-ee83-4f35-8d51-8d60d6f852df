import { z } from 'zod';
import { BaseEntitySchema, ExecutionStatusSchema } from '@synapseai/core';

// Workflow Core Types
export const WorkflowTypeSchema = z.enum([
  'LINEAR',
  'PARALLEL',
  'CONDITIONAL',
  'SCHEDULED',
  'EVENT_DRIVEN',
  'HUMAN_IN_LOOP',
  'MULTI_AGENT',
]);

export const WorkflowNodeTypeSchema = z.enum([
  'START',
  'ACTION',
  'DECISION',
  'MERGE',
  'PARALLEL',
  'END',
  'WAIT',
  'NOTIFY',
  'FETCH',
  'PROCESS',
]);

export const WorkflowConfigSchema = BaseEntitySchema.extend({
  name: z.string().min(1),
  description: z.string(),
  type: WorkflowTypeSchema,
  nodes: z.array(z.object({
    id: z.string(),
    type: WorkflowNodeTypeSchema,
    config: z.record(z.unknown()),
    metadata: z.record(z.unknown()).optional(),
    inputs: z.record(z.string().array()).default({}),
    outputs: z.record(z.string().array()).default({}),
  })),
  edges: z.array(z.object({
    from: z.string(),
    to: z.string(),
    condition: z.string().optional(),
    metadata: z.record(z.unknown()).optional(),
  })).default([]),
  triggers: z.array(z.object({
    type: z.enum(['MANUAL', 'CRON', 'WEBHOOK', 'EVENT']),
    config: z.record(z.unknown()).default({}),
  })).default([]),
  metadata: z.record(z.unknown()).default({}),
  isActive: z.boolean().default(true),
});

export type WorkflowType = z.infer<typeof WorkflowTypeSchema>;
export type WorkflowNodeType = z.infer<typeof WorkflowNodeTypeSchema>;
export type WorkflowConfig = z.infer<typeof WorkflowConfigSchema>;

// Workflow Execution Types
export const WorkflowExecutionContextSchema = z.object({
  workflowId: z.string(),
  sessionId: z.string().optional(),
  userId: z.string().optional(),
  tenantId: z.string(),
  config: WorkflowConfigSchema,
  startTime: z.date(),
  variables: z.record(z.unknown()).default({}),
});

export const WorkflowExecutionSchema = z.object({
  id: z.string(),
  workflowId: z.string(),
  status: ExecutionStatusSchema,
  startTime: z.date(),
  endTime: z.date().optional(),
  duration: z.number().nonnegative().optional(),
  result: z.record(z.unknown()).optional(),
  error: z.string().optional(),
  metadata: z.record(z.unknown()).default({}),
});

export const WorkflowNodeStateSchema = z.object({
  nodeId: z.string(),
  status: ExecutionStatusSchema,
  startTime: z.date(),
  endTime: z.date().optional(),
  result: z.record(z.unknown()).optional(),
  error: z.string().optional(),
  metadata: z.record(z.unknown()).default({}),
});

export type WorkflowExecutionContext = z.infer<typeof WorkflowExecutionContextSchema>;
export type WorkflowExecution = z.infer<typeof WorkflowExecutionSchema>;
export type WorkflowNodeState = z.infer<typeof WorkflowNodeStateSchema>;

// Workflow Event Types
export const WorkflowEventSchema = z.enum([
  'WORKFLOW_STARTED',
  'WORKFLOW_COMPLETED',
  'WORKFLOW_FAILED',
  'WORKFLOW_PAUSED',
  'WORKFLOW_RESUMED',
  'WORKFLOW_CANCELLED',
  'NODE_STARTED',
  'NODE_COMPLETED',
  'NODE_FAILED',
  'NODE_PAUSED',
  'NODE_RESUMED',
  'NODE_CANCELLED',
]);

export type WorkflowEvent = z.infer<typeof WorkflowEventSchema>;

// Workflow Registry Interface
export interface WorkflowRegistry {
  register(config: WorkflowConfig): Promise<void>;
  unregister(workflowId: string): Promise<void>;
  get(workflowId: string): Promise<WorkflowConfig | null>;
  list(): Promise<WorkflowConfig[]>;
  createExecution(workflowId: string, context?: WorkflowExecutionContext): Promise<WorkflowExecution>;
  stopExecution(executionId: string): Promise<void>;
  pauseExecution(executionId: string): Promise<void>;
  resumeExecution(executionId: string): Promise<void>;
}

// Workflow Factory Interface
export interface WorkflowFactory {
  create(config: WorkflowConfig): Promise<Workflow>;
  validate(config: unknown): WorkflowConfig;
  getDefaultConfig(): Partial<WorkflowConfig>;
  getSupportedTypes(): WorkflowType[];
}

// Base Workflow Interface
export interface Workflow {
  readonly id: string;
  readonly config: WorkflowConfig;
  readonly type: WorkflowType;

  execute(context: WorkflowExecutionContext): Promise<WorkflowExecution>;
  pause(): Promise<void>;
  resume(): Promise<void>;
  cancel(): Promise<void>;

  // Validation
  validateConfig(config: unknown): WorkflowConfig;

  // Events
  on(event: WorkflowEvent, listener: (...args: unknown[]) => void): this;
}

// Workflow Execution History
export const WorkflowExecutionHistorySchema = z.object({
  id: z.string(),
  workflowId: z.string(),
  execution: WorkflowExecutionSchema,
  nodesState: z.array(WorkflowNodeStateSchema),
  createdAt: z.date(),
});

export type WorkflowExecutionHistory = z.infer<typeof WorkflowExecutionHistorySchema>;

// Workflow Node Definition
export const WorkflowNodeDefinitionSchema = z.object({
  id: z.string(),
  type: WorkflowNodeTypeSchema,
  nodeSchema: z.record(z.unknown()),
  inputSchema: z.record(z.unknown()),
  outputSchema: z.record(z.unknown()),
  isAvailable: z.boolean().default(true),
  metadata: z.record(z.unknown()).default({}),
});

export type WorkflowNodeDefinition = z.infer<typeof WorkflowNodeDefinitionSchema>;
