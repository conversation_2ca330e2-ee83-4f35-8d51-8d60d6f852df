# =============================================================================
# SynapseAI Environment Configuration
# =============================================================================
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# Database Configuration
# =============================================================================
DATABASE_URL="postgresql://username:password@localhost:5432/synapseai_dev"
DIRECT_URL="postgresql://username:password@localhost:5432/synapseai_dev"

# Redis Configuration
REDIS_URL="redis://localhost:6379"

# =============================================================================
# Authentication & Security
# =============================================================================
NEXTAUTH_SECRET="your-nextauth-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# JWT Configuration
JWT_SECRET="your-jwt-secret-key-here"
JWT_EXPIRES_IN="7d"
REFRESH_TOKEN_SECRET="your-refresh-token-secret-here"
REFRESH_TOKEN_EXPIRES_IN="30d"

# =============================================================================
# AI Provider APIs
# =============================================================================

# OpenAI
OPENAI_API_KEY="sk-your-openai-api-key-here"
OPENAI_ORGANIZATION_ID="org-your-organization-id"

# Anthropic Claude
ANTHROPIC_API_KEY="sk-ant-your-anthropic-api-key-here"

# Google Gemini
GOOGLE_API_KEY="your-google-api-key-here"

# Mistral AI
MISTRAL_API_KEY="your-mistral-api-key-here"

# Groq
GROQ_API_KEY="gsk_your-groq-api-key-here"

# DeepSeek
DEEPSEEK_API_KEY="your-deepseek-api-key-here"

# Hugging Face
HUGGINGFACE_API_KEY="hf_your-huggingface-api-key-here"

# Local AI (Ollama)
OLLAMA_BASE_URL="http://localhost:11434"

# =============================================================================
# Application Configuration
# =============================================================================
NODE_ENV="development"
PORT="3000"
API_PORT="3001"

# APIX WebSocket Configuration
APIX_PORT="3002"
APIX_WS_PORT="3003"

# =============================================================================
# File Storage & Media
# =============================================================================
# AWS S3 (for file uploads and storage)
AWS_ACCESS_KEY_ID="your-aws-access-key-id"
AWS_SECRET_ACCESS_KEY="your-aws-secret-access-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="synapseai-storage"

# Or use local file storage
STORAGE_TYPE="local" # "local" | "s3" | "gcs"
STORAGE_PATH="./uploads"

# =============================================================================
# Email Configuration
# =============================================================================
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-app-password"
FROM_EMAIL="<EMAIL>"

# =============================================================================
# Monitoring & Analytics
# =============================================================================
# Sentry (Error Tracking)
NEXT_PUBLIC_SENTRY_DSN="your-sentry-dsn-here"
SENTRY_ORG="synapseai"
SENTRY_PROJECT="synapseai-web"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"

# PostHog (Analytics)
NEXT_PUBLIC_POSTHOG_KEY="your-posthog-key-here"
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"

# =============================================================================
# External Integrations
# =============================================================================
# Stripe (Payments)
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="whsec_your-webhook-secret"

# GitHub (for integrations)
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Discord (for notifications)
DISCORD_BOT_TOKEN="your-discord-bot-token"
DISCORD_WEBHOOK_URL="your-discord-webhook-url"

# =============================================================================
# Development & Testing
# =============================================================================
# Enable debug mode
DEBUG="synapseai:*"

# Test database (for running tests)
TEST_DATABASE_URL="postgresql://username:password@localhost:5432/synapseai_test"

# Playwright E2E testing
PLAYWRIGHT_BASE_URL="http://localhost:3000"

# =============================================================================
# Feature Flags
# =============================================================================
ENABLE_ANALYTICS="true"
ENABLE_MONITORING="true"
ENABLE_DEBUGGING="true"
ENABLE_EXPERIMENTAL_FEATURES="false"

# =============================================================================
# Rate Limiting & Quotas
# =============================================================================
RATE_LIMIT_WINDOW_MS="900000" # 15 minutes
RATE_LIMIT_MAX_REQUESTS="100"

# AI Provider Rate Limits
OPENAI_RATE_LIMIT="60" # requests per minute
ANTHROPIC_RATE_LIMIT="40"
GEMINI_RATE_LIMIT="100"

# =============================================================================
# Logging Configuration
# =============================================================================
LOG_LEVEL="info" # "error" | "warn" | "info" | "debug"
LOG_FORMAT="json" # "json" | "pretty"
LOG_FILE_PATH="./logs/app.log"
