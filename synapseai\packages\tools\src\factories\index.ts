import { z } from 'zod';
import { 
  Tool, 
  ToolFactory, 
  ToolConfig, 
  ToolType, 
  ToolCategory 
} from '../types';
import { FunctionCallerTool, ApiFetcherTool, BaseToolFactory } from '../runtime';

// Tool Factory Implementation
export class FunctionCallerToolFactory extends BaseToolFactory {
  async create(config: ToolConfig): Promise<Tool> {
    const validatedConfig = this.validate(config);
    return new FunctionCallerTool(validatedConfig);
  }

  getDefaultConfig(): Partial<ToolConfig> {
    return {
      name: 'Function Caller Tool',
      description: 'Executes functions with given arguments',
      type: 'FUNCTION_CALLER',
      category: 'UTILITY',
      inputSchema: { functionName: 'string', arguments: 'object' },
      outputSchema: { result: 'object' },
    };
  }

  getSupportedTypes(): ToolType[] {
    return ['FUNCTION_CALLER'];
  }
}

export class ApiFetcherToolFactory extends BaseToolFactory {
  async create(config: ToolConfig): Promise<Tool> {
    const validatedConfig = this.validate(config);
    return new ApiFetcherTool(validatedConfig);
  }

  getDefaultConfig(): Partial<ToolConfig> {
    return {
      name: 'API Fetcher Tool',
      description: 'Fetches data from APIs',
      type: 'API_FETCHER',
      category: 'API_INTEGRATION',
      inputSchema: { url: 'string', method: 'string', headers: 'object', body: 'object' },
      outputSchema: { data: 'object', status: 'number' },
    };
  }

  getSupportedTypes(): ToolType[] {
    return ['API_FETCHER'];
  }
}

// Tool Factory Registry
export class ToolFactoryRegistry {
  private factories = new Map<ToolType, ToolFactory>();

  constructor() {
    // Register default factories
    this.register('FUNCTION_CALLER', new FunctionCallerToolFactory());
    this.register('API_FETCHER', new ApiFetcherToolFactory());
  }

  register(type: ToolType, factory: ToolFactory): void {
    this.factories.set(type, factory);
  }

  unregister(type: ToolType): void {
    this.factories.delete(type);
  }

  get(type: ToolType): ToolFactory | null {
    return this.factories.get(type) || null;
  }

  list(): ToolType[] {
    return Array.from(this.factories.keys());
  }

  async createTool(type: ToolType, config: Partial<ToolConfig>): Promise<Tool> {
    const factory = this.get(type);
    if (!factory) {
      throw new Error(`No factory registered for tool type: ${type}`);
    }

    const defaultConfig = factory.getDefaultConfig();
    const fullConfig = { ...defaultConfig, ...config, type };
    
    return factory.create(factory.validate(fullConfig));
  }
}

// Default tool factory registry instance
export const defaultToolFactoryRegistry = new ToolFactoryRegistry();
