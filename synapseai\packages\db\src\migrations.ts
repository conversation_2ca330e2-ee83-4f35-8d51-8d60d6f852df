import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

export interface MigrationResult {
  success: boolean
  output: string
  error?: string
}

export async function runMigrations(): Promise<MigrationResult> {
  try {
    const { stdout, stderr } = await execAsync('npx prisma migrate deploy')
    return {
      success: true,
      output: stdout,
      error: stderr || undefined,
    }
  } catch (error) {
    return {
      success: false,
      output: '',
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

export async function resetDatabase(): Promise<MigrationResult> {
  try {
    const { stdout, stderr } = await execAsync('npx prisma migrate reset --force')
    return {
      success: true,
      output: stdout,
      error: stderr || undefined,
    }
  } catch (error) {
    return {
      success: false,
      output: '',
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

export async function generatePrismaClient(): Promise<MigrationResult> {
  try {
    const { stdout, stderr } = await execAsync('npx prisma generate')
    return {
      success: true,
      output: stdout,
      error: stderr || undefined,
    }
  } catch (error) {
    return {
      success: false,
      output: '',
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

export async function runSeed(): Promise<MigrationResult> {
  try {
    const { stdout, stderr } = await execAsync('npx tsx prisma/seed.ts')
    return {
      success: true,
      output: stdout,
      error: stderr || undefined,
    }
  } catch (error) {
    return {
      success: false,
      output: '',
      error: error instanceof Error ? error.message : String(error),
    }
  }
}
