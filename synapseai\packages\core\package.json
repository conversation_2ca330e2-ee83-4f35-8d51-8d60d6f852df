{"name": "@synapseai/core", "version": "0.1.0", "type": "module", "description": "SynapseAI Core - Business Logic and Shared Types", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./agents": {"types": "./dist/agents/index.d.ts", "import": "./dist/agents/index.js"}, "./tools": {"types": "./dist/tools/index.d.ts", "import": "./dist/tools/index.js"}, "./workflows": {"types": "./dist/workflows/index.d.ts", "import": "./dist/workflows/index.js"}, "./providers": {"types": "./dist/providers/index.d.ts", "import": "./dist/providers/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "import": "./dist/utils/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && tsc-alias", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@ai-sdk/anthropic": "^1.0.5", "@ai-sdk/google": "^1.0.8", "@ai-sdk/mistral": "^1.0.4", "@ai-sdk/openai": "^1.0.8", "ai": "^4.0.38", "nanoid": "^5.0.9", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.8.4", "tsc-alias": "^1.8.10", "typescript": "^5.6.3", "vitest": "^2.1.9"}, "peerDependencies": {"@synapseai/db": "workspace:*"}}