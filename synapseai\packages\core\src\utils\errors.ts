import { z } from 'zod';
import { ErrorCode, ApiError, ApiErrorSchema } from '../types';

export abstract class BaseError extends Error {
  public abstract readonly code: ErrorCode;
  public readonly timestamp: Date;
  public readonly requestId: string;
  public readonly details: Record<string, unknown>;

  constructor(
    message: string,
    details: Record<string, unknown> = {},
    requestId?: string
  ) {
    super(message);
    this.name = this.constructor.name;
    this.timestamp = new Date();
    this.requestId = requestId || this.generateRequestId();
    this.details = details;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  public toApiError(path?: string): ApiError {
    return ApiErrorSchema.parse({
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
      requestId: this.requestId,
      path,
    });
  }

  private generateRequestId(): string {
    return `req_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export class ValidationError extends BaseError {
  public readonly code = 'VALIDATION_ERROR' as const;

  constructor(message: string, zodError?: z.ZodError, requestId?: string) {
    const details = zodError ? { zodError: zodError.flatten() } : {};
    super(message, details, requestId);
  }

  static fromZodError(zodError: z.ZodError, requestId?: string): ValidationError {
    const message = `Validation failed: ${zodError.errors.map(e => `${e.path.join('.')} - ${e.message}`).join('; ')}`;
    return new ValidationError(message, zodError, requestId);
  }
}

export class AuthenticationError extends BaseError {
  public readonly code = 'AUTHENTICATION_ERROR' as const;

  constructor(message: string = 'Authentication required', details?: Record<string, unknown>, requestId?: string) {
    super(message, details, requestId);
  }
}

export class AuthorizationError extends BaseError {
  public readonly code = 'AUTHORIZATION_ERROR' as const;

  constructor(message: string = 'Insufficient permissions', details?: Record<string, unknown>, requestId?: string) {
    super(message, details, requestId);
  }
}

export class NotFoundError extends BaseError {
  public readonly code = 'NOT_FOUND_ERROR' as const;

  constructor(resource: string, id?: string, requestId?: string) {
    const message = id ? `${resource} with id '${id}' not found` : `${resource} not found`;
    super(message, { resource, id }, requestId);
  }
}

export class ConflictError extends BaseError {
  public readonly code = 'CONFLICT_ERROR' as const;

  constructor(message: string, details?: Record<string, unknown>, requestId?: string) {
    super(message, details, requestId);
  }
}

export class RateLimitError extends BaseError {
  public readonly code = 'RATE_LIMIT_ERROR' as const;

  constructor(
    limit: number,
    window: string,
    retryAfter?: number,
    requestId?: string
  ) {
    const message = `Rate limit exceeded: ${limit} requests per ${window}`;
    const details = { limit, window, retryAfter };
    super(message, details, requestId);
  }
}

export class ProviderError extends BaseError {
  public readonly code = 'PROVIDER_ERROR' as const;

  constructor(
    provider: string,
    message: string,
    details?: Record<string, unknown>,
    requestId?: string
  ) {
    super(`Provider ${provider} error: ${message}`, { provider, ...details }, requestId);
  }
}

export class ExecutionError extends BaseError {
  public readonly code = 'EXECUTION_ERROR' as const;

  constructor(
    operation: string,
    message: string,
    details?: Record<string, unknown>,
    requestId?: string
  ) {
    super(`Execution failed for ${operation}: ${message}`, { operation, ...details }, requestId);
  }
}

export class NetworkError extends BaseError {
  public readonly code = 'NETWORK_ERROR' as const;

  constructor(message: string, details?: Record<string, unknown>, requestId?: string) {
    super(`Network error: ${message}`, details, requestId);
  }
}

export class InternalError extends BaseError {
  public readonly code = 'INTERNAL_ERROR' as const;

  constructor(message: string = 'Internal server error', details?: Record<string, unknown>, requestId?: string) {
    super(message, details, requestId);
  }
}

// Error utility functions
export const isBaseError = (error: unknown): error is BaseError => {
  return error instanceof BaseError;
};

export const handleError = (error: unknown, defaultMessage?: string, requestId?: string): BaseError => {
  if (isBaseError(error)) {
    return error;
  }

  if (error instanceof z.ZodError) {
    return ValidationError.fromZodError(error, requestId);
  }

  if (error instanceof Error) {
    return new InternalError(error.message, { originalError: error.message }, requestId);
  }

  return new InternalError(defaultMessage || 'Unknown error occurred', { originalError: error }, requestId);
};

export const createErrorHandler = (context: string) => {
  return (error: unknown, requestId?: string): BaseError => {
    const baseError = handleError(error, `Error in ${context}`, requestId);
    baseError.details.context = context;
    return baseError;
  };
};
