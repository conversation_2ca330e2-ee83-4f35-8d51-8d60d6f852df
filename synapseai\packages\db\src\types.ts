// Additional TypeScript types for SynapseAI database operations

export interface DatabaseContext {
  tenantId?: string
  userId?: string
  userRole?: string
  ipAddress?: string
  userAgent?: string
}

export interface TenantContext {
  id: string
  name: string
  slug: string
  settings: Record<string, any>
}

export interface UserContext {
  id: string
  email: string
  role: string
  tenantId: string
}

export interface QueryOptions {
  include?: Record<string, boolean | QueryOptions>
  select?: Record<string, boolean>
  where?: Record<string, any>
  orderBy?: Record<string, 'asc' | 'desc'>
  skip?: number
  take?: number
}

export interface PaginationOptions {
  page?: number
  limit?: number
  cursor?: string
}

export interface SearchOptions extends PaginationOptions {
  query?: string
  filters?: Record<string, any>
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface VectorSearchOptions extends SearchOptions {
  vector?: number[]
  similarity?: number
  maxResults?: number
}

// Multi-tenant query wrappers
export interface TenantAwareQuery<T = any> {
  tenantId: string
  data: T
  context?: DatabaseContext
}

export interface AuditableOperation<T = any> {
  action: 'create' | 'update' | 'delete'
  tableName: string
  recordId?: string
  oldData?: T
  newData?: T
  metadata?: Record<string, any>
}

// Row Level Security types
export interface RLSContext {
  tenant_id?: string
  user_id?: string
  user_role?: string
}

// Embedding and vector search types
export interface EmbeddingVector {
  content: string
  vector: number[]
  metadata?: Record<string, any>
}

export interface SimilaritySearchResult {
  id: string
  content: string
  similarity: number
  metadata?: Record<string, any>
}

// Session and memory types
export interface SessionMemory {
  shortTerm: Record<string, any>
  longTerm: Record<string, any>
  context: Record<string, any>
  metadata: Record<string, any>
}

export interface SessionContext {
  id: string
  tenantId: string
  userId?: string
  agentId?: string
  workflowId?: string
  memory: SessionMemory
  settings: Record<string, any>
}

// Workflow execution types
export interface WorkflowExecutionContext {
  workflowId: string
  sessionId?: string
  input: Record<string, any>
  variables: Record<string, any>
  metadata: Record<string, any>
}

export interface NodeExecutionResult {
  success: boolean
  output?: any
  error?: string
  metadata?: Record<string, any>
  executionTime?: number
}

// Provider and AI model types
export interface ProviderCredentials {
  apiKey?: string
  endpoint?: string
  model?: string
  customConfig?: Record<string, any>
}

export interface ModelCapabilities {
  chat: boolean
  completion: boolean
  embedding: boolean
  vision: boolean
  functionCalling: boolean
  streaming: boolean
}

export interface TokenUsage {
  inputTokens: number
  outputTokens: number
  totalTokens: number
  cost?: number
}

// Knowledge base and document types
export interface DocumentChunk {
  id: string
  content: string
  chunkIndex: number
  vector?: number[]
  metadata: Record<string, any>
}

export interface DocumentProcessingResult {
  success: boolean
  chunks: DocumentChunk[]
  embeddings?: EmbeddingVector[]
  error?: string
  processingTime?: number
}

// API and authentication types
export interface ApiKeyPermissions {
  read: string[]
  write: string[]
  admin: string[]
}

export interface JWTPayload {
  userId: string
  tenantId: string
  role: string
  permissions: string[]
  iat?: number
  exp?: number
}
