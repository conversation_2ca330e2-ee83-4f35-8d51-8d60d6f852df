{"name": "@synapseai/db", "version": "0.1.0", "type": "module", "description": "SynapseAI Database - Prisma Schema and Client", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./client": {"types": "./dist/client.d.ts", "import": "./dist/client.js"}, "./schema": {"types": "./dist/schema.d.ts", "import": "./dist/schema.js"}, "./migrations": {"types": "./dist/migrations.d.ts", "import": "./dist/migrations.js"}, "./seed": {"types": "./dist/seed.d.ts", "import": "./dist/seed.js"}}, "files": ["dist", "prisma", "README.md"], "scripts": {"build": "prisma generate && tsc && tsc-alias", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "test": "vitest", "test:coverage": "vitest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:format": "prisma format"}, "dependencies": {"@prisma/client": "^5.22.0", "bcryptjs": "^2.4.3", "zod": "^3.24.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22.8.4", "prisma": "^5.22.0", "tsc-alias": "^1.8.10", "tsx": "^4.19.2", "typescript": "^5.6.3", "vitest": "^2.1.9"}}