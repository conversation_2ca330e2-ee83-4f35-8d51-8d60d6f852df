// APIX Protocol Constants
export const APIX_CHANNELS = {
  AGENT_EVENTS: 'agent-events',
  TOOL_EVENTS: 'tool-events',
  WORKFLOW_EVENTS: 'workflow-events',
  PROVIDER_EVENTS: 'provider-events',
  SYSTEM_EVENTS: 'system-events',
} as const;

export const APIX_EVENT_TYPES = {
  TOOL_CALL_START: 'tool_call_start',
  TOOL_CALL_RESULT: 'tool_call_result',
  TOOL_CALL_ERROR: 'tool_call_error',
  THINKING_STATUS: 'thinking_status',
  TEXT_CHUNK: 'text_chunk',
  STATE_UPDATE: 'state_update',
  REQUEST_USER_INPUT: 'request_user_input',
  SESSION_START: 'session_start',
  SESSION_END: 'session_end',
  ERROR_OCCURRED: 'error_occurred',
  FALLBACK_TRIGGERED: 'fallback_triggered',
} as const;

// Default event configurations
export const DEFAULT_EVENT_CONFIG = {
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  TIMEOUT: 30000,
  QUEUE_SIZE: 1000,
} as const;
