{"name": "@synapseai/apix", "version": "0.1.0", "private": true, "type": "module", "description": "SynapseAI APIX - Real-time WebSocket API Service", "main": "dist/index.js", "scripts": {"build": "tsc && tsc-alias", "dev": "tsx watch --clear-screen=false src/index.ts", "start": "node dist/index.js", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "test": "vitest", "test:coverage": "vitest --coverage", "test:e2e": "vitest --config vitest.config.e2e.ts", "docker:build": "docker build -t synapseai/apix .", "docker:run": "docker run -p 3002:3002 -p 3003:3003 synapseai/apix"}, "dependencies": {"@fastify/cors": "^10.0.1", "@fastify/helmet": "^12.0.1", "@fastify/jwt": "^9.0.1", "@fastify/rate-limit": "^10.1.1", "@fastify/redis": "^7.0.1", "@fastify/swagger": "^9.1.0", "@fastify/swagger-ui": "^5.0.1", "@fastify/websocket": "^11.0.1", "@synapseai/core": "workspace:*", "@synapseai/db": "workspace:*", "bull": "^4.16.3", "dotenv": "^16.4.7", "fastify": "^5.1.0", "ioredis": "^5.4.1", "nanoid": "^5.0.9", "pino": "^9.5.0", "pino-pretty": "^13.0.0", "ws": "^8.18.0", "zod": "^3.24.1"}, "devDependencies": {"@types/bull": "^4.10.0", "@types/node": "^22.8.4", "@types/ws": "^8.5.13", "tsc-alias": "^1.8.10", "tsx": "^4.19.2", "typescript": "^5.6.3", "vitest": "^2.1.9"}}