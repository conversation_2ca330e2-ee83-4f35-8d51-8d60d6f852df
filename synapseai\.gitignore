# Dependencies
node_modules/
.pnpm-store/
.pnpm-debug.log*
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
.next/
out/
coverage/
.turbo/
.cache/
.parcel-cache/
.nyc_output/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Logs
logs/
*.log
lerna-debug.log*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# TypeScript
*.tsbuildinfo

# Temporary folders
tmp/
temp/

# Database
*.db
*.sqlite
*.sqlite3

# Prisma
prisma/generated/

# Test coverage
coverage/
.nyc_output/

# Storybook build outputs
storybook-static/

# Local Netlify folder
.netlify

# Vercel
.vercel

# Sentry
.sentryclirc

# Docker
.dockerignore

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Local development
*.local
.env.example.local

# AI/ML models
*.model
*.pkl
*.h5
*.onnx

# Large files
*.zip
*.rar
*.7z
*.tar.gz

# Generated documentation
docs/dist/
docs/.vitepress/cache/
docs/.vitepress/dist/

# Generated API schemas
schemas/generated/
openapi.json
graphql.schema

# Changeset
.changeset/pre.json

# Husky
.husky/_
