// Export main client
export { SynapseClient } from './client';

// Export all client components
export * from './client/http';
export * from './client/websocket';

// Export types
export * from './types';

// Export package metadata
export const version = '0.1.0';
export const name = '@synapseai/sdk';

// Factory function for creating client instances
export function createSynapseClient(config: {
  baseUrl: string;
  apiKey?: string;
  tenantId?: string;
  timeout?: number;
  retries?: number;
  enableWebSocket?: boolean;
  wsUrl?: string;
  headers?: Record<string, string>;
}) {
  const { SynapseClient } = require('./client');
  return new SynapseClient(config);
}
