{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "SynapseAI",
  "compilerOptions": {
    // Type Checking
    "strict": true,
    "exactOptionalPropertyTypes": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noPropertyAccessFromIndexSignature": true,
    "noUncheckedIndexedAccess": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,

    // Modules
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,

    // Emit
    "noEmit": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,

    // JavaScript Support
    "allowJs": true,
    "checkJs": false,

    // Interop Constraints
    "isolatedModules": true,
    "verbatimModuleSyntax": false,

    // Language and Environment
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "useDefineForClassFields": true,

    // Projects
    "incremental": true,
    "composite": true,

    // Completeness
    "skipLibCheck": true,

    // Base URL and Paths
    "baseUrl": ".",
    "paths": {
      "@synapseai/core/*": ["./packages/core/src/*"],
      "@synapseai/db/*": ["./packages/db/src/*"],
      "@synapseai/sdk/*": ["./packages/sdk/src/*"],
      "@/*": ["./src/*"]
    },

    // JSX
    "jsx": "preserve",
    "jsxImportSource": "react"
  },
  "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.mjs", "**/*.json"],
  "exclude": ["node_modules", "dist", "build", ".next", "coverage", "**/*.config.js", "**/*.config.mjs"]
}
