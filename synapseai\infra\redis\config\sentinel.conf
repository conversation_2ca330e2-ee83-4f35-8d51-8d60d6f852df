# Redis Sentinel Configuration for SynapseAI
# High availability and automatic failover

# Basic Settings
port 26379
bind 0.0.0.0

# Sentinel Configuration
sentinel monitor synapseai-master redis-master 6379 1
sentinel auth-pass synapseai-master synapseai_redis_password
sentinel down-after-milliseconds synapseai-master 5000
sentinel parallel-syncs synapseai-master 1
sentinel failover-timeout synapseai-master 10000

# Logging
logfile /var/log/redis/sentinel.log
loglevel notice

# Security
requirepass synapseai_redis_password

# Notification Scripts (for production)
# sentinel notification-script synapseai-master /opt/scripts/notify.sh
# sentinel client-reconfig-script synapseai-master /opt/scripts/reconfig.sh

# <PERSON>y <PERSON> (security)
sentinel deny-scripts-reconfig yes
