export * from '../prisma/schema.prisma' 

// Re-export Prisma client types and enums for convenience
export type {
  // Core types
  Tenant,
  User,
  TenantUser,
  Agent,
  Tool,
  Workflow,
  Session,
  ProviderConfig,
  KnowledgeBase,
  Document,
  Embedding,
  ApiKey,
  AuditLog,
  TenantQuota,
  
  // Enums
  TenantStatus,
  UserRole,
  UserStatus,
  AgentType,
  AgentStatus,
  ToolType,
  ToolStatus,
  WorkflowStatus,
  SessionStatus,
  ProviderType,
  ProviderStatus,
  ExecutionStatus,
  AuditAction,
  
  // Input types for creating resources
  TenantCreateInput,
  UserCreateInput,
  AgentCreateInput,
  ToolCreateInput,
  WorkflowCreateInput,
  SessionCreateInput,
  
  // Update types
  TenantUpdateInput,
  UserUpdateInput,
  AgentUpdateInput,
  ToolUpdateInput,
  WorkflowUpdateInput,
  SessionUpdateInput,
} from './generated'
