{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "build/**", "out/**"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", "src/**/*.{ts,tsx,js,jsx}", "test/**/*.{ts,tsx,js,jsx}", "__tests__/**/*.{ts,tsx,js,jsx}", "*.config.{js,ts,mjs}", "vitest.config.{js,ts,mjs}", "jest.config.{js,ts,mjs}"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["build"], "cache": false, "inputs": ["$TURBO_DEFAULT$", "e2e/**/*.{ts,js}", "playwright.config.{js,ts}", "cypress.config.{js,ts}"]}, "lint": {"inputs": ["$TURBO_DEFAULT$", ".eslintrc*", ".es<PERSON><PERSON><PERSON>", "eslint.config.*"], "outputs": []}, "lint:fix": {"cache": false, "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", ".es<PERSON><PERSON><PERSON>", "eslint.config.*"], "outputs": []}, "typecheck": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "tsconfig.json", "tsconfig.*.json"], "outputs": []}, "clean": {"cache": false, "outputs": []}, "db:generate": {"cache": false, "inputs": ["prisma/schema.prisma", ".env*"], "outputs": ["node_modules/.prisma/**", "prisma/generated/**"]}, "db:push": {"cache": false, "inputs": ["prisma/schema.prisma", ".env*"]}, "db:migrate": {"cache": false, "inputs": ["prisma/schema.prisma", "prisma/migrations/**", ".env*"]}, "db:studio": {"cache": false, "persistent": true}}, "globalDependencies": [".env", ".env.local", ".env.development", ".env.production", "package.json", "pnpm-workspace.yaml", "turbo.json", "tsconfig.json", ".eslintrc*", ".prettierrc*", "tailwind.config.*"], "globalEnv": ["NODE_ENV", "DATABASE_URL", "REDIS_URL", "NEXTAUTH_SECRET", "NEXTAUTH_URL", "OPENAI_API_KEY", "ANTHROPIC_API_KEY"]}