import { z } from 'zod';
import { EventEmitter } from 'events';
import {
  SynapseClientConfig,
  SynapseClientConfigSchema,
  HttpRequest,
  HttpResponse,
  HttpMethod,
  WebSocketMessage,
  ApiResponse,
  ClientEvent,
  ClientState,
  RequestInterceptor,
  ResponseInterceptor,
  ErrorInterceptor,
} from '../types';
import { WebSocketManager } from './websocket';
import { HttpClient } from './http';

export class SynapseClient extends EventEmitter {
  private config: SynapseClientConfig;
  private httpClient: HttpClient;
  private wsManager: WebSocketManager | null = null;
  private state: ClientState;
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];
  private errorInterceptors: ErrorInterceptor[] = [];

  constructor(config: Partial<SynapseClientConfig>) {
    super();
    this.config = SynapseClientConfigSchema.parse(config);
    this.httpClient = new HttpClient(this.config);
    this.state = {
      isConnected: false,
      isReconnecting: false,
      connectionCount: 0,
      requestCount: 0,
      errorCount: 0,
    };

    this.setupHttpInterceptors();
    if (this.config.enableWebSocket) {
      this.initializeWebSocket();
    }
  }

  // HTTP Methods
  public async get<T = unknown>(
    url: string,
    params?: Record<string, unknown>,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'GET', url, params, headers });
  }

  public async post<T = unknown>(
    url: string,
    data?: unknown,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'POST', url, data, headers });
  }

  public async put<T = unknown>(
    url: string,
    data?: unknown,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'PUT', url, data, headers });
  }

  public async patch<T = unknown>(
    url: string,
    data?: unknown,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'PATCH', url, data, headers });
  }

  public async delete<T = unknown>(
    url: string,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'DELETE', url, headers });
  }

  private async request<T = unknown>(requestConfig: HttpRequest): Promise<ApiResponse<T>> {
    try {
      this.emit('request:start', requestConfig);
      this.state.requestCount++;

      // Apply request interceptors
      let processedRequest = requestConfig;
      for (const interceptor of this.requestInterceptors) {
        processedRequest = await interceptor(processedRequest);
      }

      const response = await this.httpClient.request(processedRequest);

      // Apply response interceptors
      let processedResponse = response;
      for (const interceptor of this.responseInterceptors) {
        processedResponse = await interceptor(processedResponse);
      }

      const apiResponse: ApiResponse<T> = {
        success: response.status >= 200 && response.status < 300,
        data: processedResponse.data as T,
        meta: {
          requestId: this.generateRequestId(),
          timestamp: new Date(),
          version: '1.0.0',
        },
      };

      this.emit('request:success', apiResponse);
      return apiResponse;
    } catch (error) {
      this.state.errorCount++;
      
      // Apply error interceptors
      let processedError = error as Error;
      for (const interceptor of this.errorInterceptors) {
        processedError = await interceptor(processedError);
      }

      const apiResponse: ApiResponse<T> = {
        success: false,
        error: {
          code: 'REQUEST_ERROR',
          message: processedError.message,
          details: { originalError: error },
        },
        meta: {
          requestId: this.generateRequestId(),
          timestamp: new Date(),
          version: '1.0.0',
        },
      };

      this.emit('request:error', apiResponse);
      return apiResponse;
    }
  }

  // WebSocket Methods
  public connectWebSocket(): void {
    if (!this.wsManager && this.config.enableWebSocket) {
      this.initializeWebSocket();
    }
    this.wsManager?.connect();
  }

  public disconnectWebSocket(): void {
    this.wsManager?.disconnect();
  }

  public sendMessage(message: Omit<WebSocketMessage, 'id' | 'timestamp'>): void {
    if (!this.wsManager) {
      throw new Error('WebSocket is not initialized');
    }
    
    const fullMessage: WebSocketMessage = {
      ...message,
      id: this.generateRequestId(),
      timestamp: new Date(),
    };

    this.wsManager.send(fullMessage);
  }

  public subscribeToChannel(channel: string, callback: (message: WebSocketMessage) => void): void {
    this.wsManager?.subscribe(channel, callback);
  }

  public unsubscribeFromChannel(channel: string): void {
    this.wsManager?.unsubscribe(channel);
  }

  // Interceptors
  public addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  public addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  public addErrorInterceptor(interceptor: ErrorInterceptor): void {
    this.errorInterceptors.push(interceptor);
  }

  public removeRequestInterceptor(interceptor: RequestInterceptor): void {
    const index = this.requestInterceptors.indexOf(interceptor);
    if (index > -1) {
      this.requestInterceptors.splice(index, 1);
    }
  }

  public removeResponseInterceptor(interceptor: ResponseInterceptor): void {
    const index = this.responseInterceptors.indexOf(interceptor);
    if (index > -1) {
      this.responseInterceptors.splice(index, 1);
    }
  }

  public removeErrorInterceptor(interceptor: ErrorInterceptor): void {
    const index = this.errorInterceptors.indexOf(interceptor);
    if (index > -1) {
      this.errorInterceptors.splice(index, 1);
    }
  }

  // State and Configuration
  public getState(): ClientState {
    return { ...this.state };
  }

  public getConfig(): SynapseClientConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<SynapseClientConfig>): void {
    this.config = SynapseClientConfigSchema.parse({ ...this.config, ...updates });
    this.httpClient.updateConfig(this.config);
  }

  // Cleanup
  public destroy(): void {
    this.wsManager?.disconnect();
    this.removeAllListeners();
  }

  private initializeWebSocket(): void {
    const wsUrl = this.config.wsUrl || this.config.baseUrl.replace(/^http/, 'ws');
    this.wsManager = new WebSocketManager({
      url: wsUrl,
      headers: this.config.headers,
    });

    this.wsManager.on('connect', () => {
      this.state.isConnected = true;
      this.state.connectionCount++;
      this.emit('websocket:connect');
    });

    this.wsManager.on('disconnect', () => {
      this.state.isConnected = false;
      this.emit('websocket:disconnect');
    });

    this.wsManager.on('message', (message: WebSocketMessage) => {
      this.emit('websocket:message', message);
    });

    this.wsManager.on('error', (error: Error) => {
      this.state.lastError = error.message;
      this.emit('websocket:error', error);
    });

    this.wsManager.on('reconnecting', () => {
      this.state.isReconnecting = true;
    });

    this.wsManager.on('reconnect', () => {
      this.state.isReconnecting = false;
    });
  }

  private setupHttpInterceptors(): void {
    // Add default request interceptor for authentication
    this.addRequestInterceptor(async (request: HttpRequest) => {
      const headers = { ...request.headers };
      
      if (this.config.apiKey) {
        headers['Authorization'] = `Bearer ${this.config.apiKey}`;
      }
      
      if (this.config.tenantId) {
        headers['X-Tenant-ID'] = this.config.tenantId;
      }

      return { ...request, headers };
    });
  }

  private generateRequestId(): string {
    return `req_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export * from '../types';
export { WebSocketManager } from './websocket';
export { HttpClient } from './http';
