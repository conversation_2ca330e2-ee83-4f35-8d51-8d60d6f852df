// SynapseAI Database Schema
// Multi-tenant, production-grade schema with Row Level Security and pgvector support

generator client {
  provider        = "prisma-client-js"
  output          = "../src/generated"
  previewFeatures = ["postgresqlExtensions", "multiSchema", "relationJoins", "fullTextSearch", "views"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  extensions = [uuid_ossp(map: "uuid-ossp"), pgcrypto, vector, btree_gin, btree_gist, pg_trgm, unaccent]
}

// ================================
// TENANT & USER MANAGEMENT
// ================================

model Tenant {
  id        String        @id @default(uuid()) @db.Uuid
  name      String        @db.VarChar(255)
  slug      String        @unique @db.VarChar(100)
  domain    String?       @db.VarChar(255)
  status    TenantStatus  @default(ACTIVE)
  settings  Json          @default("{}")
  metadata  Json          @default("{}")
  createdAt DateTime      @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime      @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  users              User[]
  tenantUsers        TenantUser[]
  agents             Agent[]
  tools              Tool[]
  workflows          Workflow[]
  sessions           Session[]
  providers          ProviderConfig[]
  knowledgeBases     KnowledgeBase[]
  documents          Document[]
  embeddings         Embedding[]
  auditLogs          AuditLog[]
  apiKeys            ApiKey[]
  quotas             TenantQuota[]
  billingInfo        BillingInfo?
  subscriptions      Subscription[]
  invitations        TenantInvitation[]

  @@map("tenants")
}

model User {
  id           String     @id @default(uuid()) @db.Uuid
  email        String     @unique @db.VarChar(320)
  passwordHash String?    @map("password_hash") @db.VarChar(255)
  firstName    String?    @map("first_name") @db.VarChar(100)
  lastName     String?    @map("last_name") @db.VarChar(100)
  avatar       String?    @db.VarChar(500)
  role         UserRole   @default(USER)
  status       UserStatus @default(ACTIVE)
  lastLoginAt  DateTime?  @map("last_login_at") @db.Timestamptz
  emailVerifiedAt DateTime? @map("email_verified_at") @db.Timestamptz
  settings     Json       @default("{}")
  metadata     Json       @default("{}")
  createdAt    DateTime   @default(now()) @map("created_at") @db.Timestamptz
  updatedAt    DateTime   @updatedAt @map("updated_at") @db.Timestamptz

  // Default tenant (required)
  tenantId String @map("tenant_id") @db.Uuid
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  tenantUsers         TenantUser[]
  sessions            Session[]
  auditLogs           AuditLog[]
  createdAgents       Agent[]          @relation("AgentCreator")
  createdTools        Tool[]           @relation("ToolCreator")
  createdWorkflows    Workflow[]       @relation("WorkflowCreator")
  apiKeys             ApiKey[]
  invitations         TenantInvitation[]
  passwordResetTokens PasswordResetToken[]
  emailVerificationTokens EmailVerificationToken[]

  @@index([tenantId])
  @@map("users")
}

model TenantUser {
  id       String           @id @default(uuid()) @db.Uuid
  tenantId String           @map("tenant_id") @db.Uuid
  userId   String           @map("user_id") @db.Uuid
  role     UserRole         @default(USER)
  status   TenantUserStatus @default(ACTIVE)
  invitedAt DateTime?       @map("invited_at") @db.Timestamptz
  joinedAt  DateTime?       @map("joined_at") @db.Timestamptz
  createdAt DateTime        @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime        @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tenantId, userId])
  @@index([tenantId])
  @@index([userId])
  @@map("tenant_users")
}

// ================================
// AGENT SYSTEM
// ================================

model Agent {
  id          String      @id @default(uuid()) @db.Uuid
  tenantId    String      @map("tenant_id") @db.Uuid
  name        String      @db.VarChar(255)
  description String?     @db.Text
  type        AgentType   @default(STANDALONE)
  status      AgentStatus @default(ACTIVE)
  config      Json        @default("{}")
  settings    Json        @default("{}")
  metadata    Json        @default("{}")
  version     Int         @default(1)
  isTemplate  Boolean     @default(false) @map("is_template")
  createdById String      @map("created_by_id") @db.Uuid
  createdAt   DateTime    @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime    @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User   @relation("AgentCreator", fields: [createdById], references: [id])

  sessions        Session[]
  workflowNodes   WorkflowNode[]
  agentTools      AgentTool[]
  agentProviders  AgentProvider[]
  knowledgeBases  AgentKnowledgeBase[]

  @@index([tenantId])
  @@index([createdById])
  @@index([type])
  @@index([status])
  @@map("agents")
}

model AgentTool {
  id       String @id @default(uuid()) @db.Uuid
  agentId  String @map("agent_id") @db.Uuid
  toolId   String @map("tool_id") @db.Uuid
  config   Json   @default("{}")
  order    Int    @default(0)
  isActive Boolean @default(true) @map("is_active")

  // Relations
  agent Agent @relation(fields: [agentId], references: [id], onDelete: Cascade)
  tool  Tool  @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@unique([agentId, toolId])
  @@index([agentId])
  @@index([toolId])
  @@map("agent_tools")
}

model AgentProvider {
  id         String @id @default(uuid()) @db.Uuid
  agentId    String @map("agent_id") @db.Uuid
  providerId String @map("provider_id") @db.Uuid
  config     Json   @default("{}")
  priority   Int    @default(0)
  isActive   Boolean @default(true) @map("is_active")

  // Relations
  agent    Agent          @relation(fields: [agentId], references: [id], onDelete: Cascade)
  provider ProviderConfig @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@unique([agentId, providerId])
  @@index([agentId])
  @@index([providerId])
  @@map("agent_providers")
}

model AgentKnowledgeBase {
  id              String @id @default(uuid()) @db.Uuid
  agentId         String @map("agent_id") @db.Uuid
  knowledgeBaseId String @map("knowledge_base_id") @db.Uuid
  config          Json   @default("{}")
  isActive        Boolean @default(true) @map("is_active")

  // Relations
  agent         Agent         @relation(fields: [agentId], references: [id], onDelete: Cascade)
  knowledgeBase KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)

  @@unique([agentId, knowledgeBaseId])
  @@index([agentId])
  @@index([knowledgeBaseId])
  @@map("agent_knowledge_bases")
}

// ================================
// TOOL SYSTEM  
// ================================

model Tool {
  id          String     @id @default(uuid()) @db.Uuid
  tenantId    String     @map("tenant_id") @db.Uuid
  name        String     @db.VarChar(255)
  description String?    @db.Text
  type        ToolType   @default(CUSTOM)
  status      ToolStatus @default(ACTIVE)
  config      Json       @default("{}")
  schema      Json       @default("{}")
  settings    Json       @default("{}")
  metadata    Json       @default("{}")
  version     Int        @default(1)
  isTemplate  Boolean    @default(false) @map("is_template")
  createdById String     @map("created_by_id") @db.Uuid
  createdAt   DateTime   @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime   @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User   @relation("ToolCreator", fields: [createdById], references: [id])

  agentTools    AgentTool[]
  workflowNodes WorkflowNode[]
  executions    ToolExecution[]

  @@index([tenantId])
  @@index([createdById])
  @@index([type])
  @@index([status])
  @@map("tools")
}

model ToolExecution {
  id         String          @id @default(uuid()) @db.Uuid
  toolId     String          @map("tool_id") @db.Uuid
  sessionId  String?         @map("session_id") @db.Uuid
  workflowId String?         @map("workflow_id") @db.Uuid
  status     ExecutionStatus @default(PENDING)
  input      Json            @default("{}")
  output     Json?
  error      Json?
  metadata   Json            @default("{}")
  startedAt  DateTime?       @map("started_at") @db.Timestamptz
  completedAt DateTime?      @map("completed_at") @db.Timestamptz
  createdAt  DateTime        @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  tool     Tool      @relation(fields: [toolId], references: [id], onDelete: Cascade)
  session  Session?  @relation(fields: [sessionId], references: [id], onDelete: SetNull)
  workflow Workflow? @relation(fields: [workflowId], references: [id], onDelete: SetNull)

  @@index([toolId])
  @@index([sessionId])
  @@index([workflowId])
  @@index([status])
  @@index([createdAt])
  @@map("tool_executions")
}

// ================================
// WORKFLOW SYSTEM
// ================================

model Workflow {
  id          String         @id @default(uuid()) @db.Uuid
  tenantId    String         @map("tenant_id") @db.Uuid
  name        String         @db.VarChar(255)
  description String?        @db.Text
  status      WorkflowStatus @default(DRAFT)
  config      Json           @default("{}")
  settings    Json           @default("{}")
  metadata    Json           @default("{}")
  version     Int            @default(1)
  isTemplate  Boolean        @default(false) @map("is_template")
  createdById String         @map("created_by_id") @db.Uuid
  createdAt   DateTime       @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime       @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User   @relation("WorkflowCreator", fields: [createdById], references: [id])

  nodes       WorkflowNode[]
  edges       WorkflowEdge[]
  executions  WorkflowExecution[]
  sessions    Session[]
  toolExecutions ToolExecution[]

  @@index([tenantId])
  @@index([createdById])
  @@index([status])
  @@map("workflows")
}

model WorkflowNode {
  id         String         @id @default(uuid()) @db.Uuid
  workflowId String         @map("workflow_id") @db.Uuid
  agentId    String?        @map("agent_id") @db.Uuid
  toolId     String?        @map("tool_id") @db.Uuid
  type       WorkflowNodeType
  name       String         @db.VarChar(255)
  config     Json           @default("{}")
  position   Json           @default("{}")
  metadata   Json           @default("{}")
  createdAt  DateTime       @default(now()) @map("created_at") @db.Timestamptz
  updatedAt  DateTime       @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  workflow     Workflow             @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  agent        Agent?               @relation(fields: [agentId], references: [id], onDelete: SetNull)
  tool         Tool?                @relation(fields: [toolId], references: [id], onDelete: SetNull)
  
  incomingEdges WorkflowEdge[]      @relation("TargetNode")
  outgoingEdges WorkflowEdge[]      @relation("SourceNode")
  executions    WorkflowNodeExecution[]

  @@index([workflowId])
  @@index([agentId])
  @@index([toolId])
  @@map("workflow_nodes")
}

model WorkflowEdge {
  id           String @id @default(uuid()) @db.Uuid
  workflowId   String @map("workflow_id") @db.Uuid
  sourceNodeId String @map("source_node_id") @db.Uuid
  targetNodeId String @map("target_node_id") @db.Uuid
  type         String @default("default") @db.VarChar(50)
  config       Json   @default("{}")
  metadata     Json   @default("{}")
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt    DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  workflow   Workflow     @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  sourceNode WorkflowNode @relation("SourceNode", fields: [sourceNodeId], references: [id], onDelete: Cascade)
  targetNode WorkflowNode @relation("TargetNode", fields: [targetNodeId], references: [id], onDelete: Cascade)

  @@index([workflowId])
  @@index([sourceNodeId])
  @@index([targetNodeId])
  @@map("workflow_edges")
}

model WorkflowExecution {
  id         String          @id @default(uuid()) @db.Uuid
  workflowId String          @map("workflow_id") @db.Uuid
  sessionId  String?         @map("session_id") @db.Uuid
  status     ExecutionStatus @default(PENDING)
  input      Json            @default("{}")
  output     Json?
  error      Json?
  metadata   Json            @default("{}")
  startedAt  DateTime?       @map("started_at") @db.Timestamptz
  completedAt DateTime?      @map("completed_at") @db.Timestamptz
  createdAt  DateTime        @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  workflow Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  session  Session? @relation(fields: [sessionId], references: [id], onDelete: SetNull)
  
  nodeExecutions WorkflowNodeExecution[]

  @@index([workflowId])
  @@index([sessionId])
  @@index([status])
  @@index([createdAt])
  @@map("workflow_executions")
}

model WorkflowNodeExecution {
  id                  String          @id @default(uuid()) @db.Uuid
  workflowExecutionId String          @map("workflow_execution_id") @db.Uuid
  nodeId              String          @map("node_id") @db.Uuid
  status              ExecutionStatus @default(PENDING)
  input               Json            @default("{}")
  output              Json?
  error               Json?
  metadata            Json            @default("{}")
  startedAt           DateTime?       @map("started_at") @db.Timestamptz
  completedAt         DateTime?       @map("completed_at") @db.Timestamptz
  createdAt           DateTime        @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  workflowExecution WorkflowExecution @relation(fields: [workflowExecutionId], references: [id], onDelete: Cascade)
  node              WorkflowNode      @relation(fields: [nodeId], references: [id], onDelete: Cascade)

  @@index([workflowExecutionId])
  @@index([nodeId])
  @@index([status])
  @@index([createdAt])
  @@map("workflow_node_executions")
}

// ================================
// SESSION MANAGEMENT
// ================================

model Session {
  id          String        @id @default(uuid()) @db.Uuid
  tenantId    String        @map("tenant_id") @db.Uuid
  userId      String?       @map("user_id") @db.Uuid
  agentId     String?       @map("agent_id") @db.Uuid
  workflowId  String?       @map("workflow_id") @db.Uuid
  status      SessionStatus @default(ACTIVE)
  context     Json          @default("{}")
  memory      Json          @default("{}")
  metadata    Json          @default("{}")
  settings    Json          @default("{}")
  startedAt   DateTime      @default(now()) @map("started_at") @db.Timestamptz
  endedAt     DateTime?     @map("ended_at") @db.Timestamptz
  lastActiveAt DateTime     @default(now()) @map("last_active_at") @db.Timestamptz
  createdAt   DateTime      @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime      @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user     User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  agent    Agent?    @relation(fields: [agentId], references: [id], onDelete: SetNull)
  workflow Workflow? @relation(fields: [workflowId], references: [id], onDelete: SetNull)

  messages           SessionMessage[]
  toolExecutions     ToolExecution[]
  workflowExecutions WorkflowExecution[]

  @@index([tenantId])
  @@index([userId])
  @@index([agentId])
  @@index([workflowId])
  @@index([status])
  @@index([lastActiveAt])
  @@map("sessions")
}

model SessionMessage {
  id        String            @id @default(uuid()) @db.Uuid
  sessionId String            @map("session_id") @db.Uuid
  role      MessageRole
  content   String            @db.Text
  metadata  Json              @default("{}")
  createdAt DateTime          @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  session Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@index([sessionId])
  @@index([createdAt])
  @@map("session_messages")
}

// ================================
// PROVIDER MANAGEMENT
// ================================

model ProviderConfig {
  id         String         @id @default(uuid()) @db.Uuid
  tenantId   String         @map("tenant_id") @db.Uuid
  name       String         @db.VarChar(100)
  type       ProviderType
  status     ProviderStatus @default(ACTIVE)
  config     Json           @default("{}")
  settings   Json           @default("{}")
  metadata   Json           @default("{}")
  capabilities Json         @default("[]")
  priority   Int            @default(0)
  costConfig Json           @default("{}")
  quotas     Json           @default("{}")
  createdAt  DateTime       @default(now()) @map("created_at") @db.Timestamptz
  updatedAt  DateTime       @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  agentProviders  AgentProvider[]
  providerMetrics ProviderMetrics[]

  @@index([tenantId])
  @@index([type])
  @@index([status])
  @@map("provider_configs")
}

model ProviderMetrics {
  id               String   @id @default(uuid()) @db.Uuid
  providerConfigId String   @map("provider_config_id") @db.Uuid
  totalRequests    Int      @default(0) @map("total_requests")
  successfulRequests Int    @default(0) @map("successful_requests")
  failedRequests   Int      @default(0) @map("failed_requests")
  averageLatency   Float    @default(0) @map("average_latency")
  totalCost        Float    @default(0) @map("total_cost")
  tokenUsage       Json     @default("{}")
  metadata         Json     @default("{}")
  date             DateTime @db.Date
  createdAt        DateTime @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  providerConfig ProviderConfig @relation(fields: [providerConfigId], references: [id], onDelete: Cascade)

  @@unique([providerConfigId, date])
  @@index([providerConfigId])
  @@index([date])
  @@map("provider_metrics")
}

// ================================
// KNOWLEDGE & EMBEDDINGS
// ================================

model KnowledgeBase {
  id          String              @id @default(uuid()) @db.Uuid
  tenantId    String              @map("tenant_id") @db.Uuid
  name        String              @db.VarChar(255)
  description String?             @db.Text
  status      KnowledgeBaseStatus @default(ACTIVE)
  config      Json                @default("{}")
  settings    Json                @default("{}")
  metadata    Json                @default("{}")
  createdAt   DateTime            @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime            @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  documents     Document[]
  agentKnowledgeBases AgentKnowledgeBase[]

  @@index([tenantId])
  @@index([status])
  @@map("knowledge_bases")
}

model Document {
  id              String        @id @default(uuid()) @db.Uuid
  tenantId        String        @map("tenant_id") @db.Uuid
  knowledgeBaseId String        @map("knowledge_base_id") @db.Uuid
  name            String        @db.VarChar(255)
  content         String        @db.Text
  contentType     String        @map("content_type") @db.VarChar(100)
  size            Int           @default(0)
  hash            String        @db.VarChar(64)
  status          DocumentStatus @default(PROCESSING)
  metadata        Json          @default("{}")
  processingLog   Json?         @map("processing_log")
  searchVector    Unsupported("vector(1536)")? @map("search_vector")
  createdAt       DateTime      @default(now()) @map("created_at") @db.Timestamptz
  updatedAt       DateTime      @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant        Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  knowledgeBase KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  
  embeddings    Embedding[]
  chunks        DocumentChunk[]

  @@index([tenantId])
  @@index([knowledgeBaseId])
  @@index([status])
  @@index([hash])
  @@map("documents")
}

model DocumentChunk {
  id         String  @id @default(uuid()) @db.Uuid
  documentId String  @map("document_id") @db.Uuid
  content    String  @db.Text
  chunkIndex Int     @map("chunk_index")
  metadata   Json    @default("{}")
  searchVector Unsupported("vector(1536)")? @map("search_vector")
  createdAt  DateTime @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  
  embeddings Embedding[]

  @@index([documentId])
  @@index([chunkIndex])
  @@map("document_chunks")
}

model Embedding {
  id              String  @id @default(uuid()) @db.Uuid
  tenantId        String  @map("tenant_id") @db.Uuid
  documentId      String? @map("document_id") @db.Uuid
  documentChunkId String? @map("document_chunk_id") @db.Uuid
  content         String  @db.Text
  vector          Unsupported("vector(1536)")
  metadata        Json    @default("{}")
  createdAt       DateTime @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  tenant        Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  document      Document?      @relation(fields: [documentId], references: [id], onDelete: Cascade)
  documentChunk DocumentChunk? @relation(fields: [documentChunkId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([documentId])
  @@index([documentChunkId])
  @@map("embeddings")
}

// ================================
// SYSTEM & SECURITY
// ================================

model ApiKey {
  id          String       @id @default(uuid()) @db.Uuid
  tenantId    String       @map("tenant_id") @db.Uuid
  userId      String       @map("user_id") @db.Uuid
  name        String       @db.VarChar(255)
  keyHash     String       @unique @map("key_hash") @db.VarChar(64)
  prefix      String       @db.VarChar(10)
  status      ApiKeyStatus @default(ACTIVE)
  permissions Json         @default("[]")
  metadata    Json         @default("{}")
  lastUsedAt  DateTime?    @map("last_used_at") @db.Timestamptz
  expiresAt   DateTime?    @map("expires_at") @db.Timestamptz
  createdAt   DateTime     @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime     @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([userId])
  @@index([keyHash])
  @@index([status])
  @@map("api_keys")
}

model AuditLog {
  id        String      @id @default(uuid()) @db.Uuid
  tenantId  String?     @map("tenant_id") @db.Uuid
  userId    String?     @map("user_id") @db.Uuid
  action    AuditAction
  tableName String      @map("table_name") @db.VarChar(100)
  recordId  String?     @map("record_id") @db.Uuid
  oldData   Json?       @map("old_data")
  newData   Json?       @map("new_data")
  ipAddress String?     @map("ip_address") @db.VarChar(45)
  userAgent String?     @map("user_agent") @db.Text
  metadata  Json        @default("{}")
  createdAt DateTime    @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  tenant Tenant? @relation(fields: [tenantId], references: [id], onDelete: SetNull)
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([tenantId])
  @@index([userId])
  @@index([action])
  @@index([tableName])
  @@index([recordId])
  @@index([createdAt])
  @@map("audit_logs")
}

model TenantQuota {
  id                    String   @id @default(uuid()) @db.Uuid
  tenantId              String   @map("tenant_id") @db.Uuid
  maxUsers              Int      @default(10) @map("max_users")
  maxAgents             Int      @default(5) @map("max_agents")
  maxTools              Int      @default(20) @map("max_tools")
  maxWorkflows          Int      @default(10) @map("max_workflows")
  maxSessions           Int      @default(100) @map("max_sessions")
  maxDocuments          Int      @default(1000) @map("max_documents")
  maxStorageBytes       BigInt   @default(1073741824) @map("max_storage_bytes") // 1GB
  maxApiCallsPerMonth   Int      @default(10000) @map("max_api_calls_per_month")
  maxTokensPerMonth     BigInt   @default(1000000) @map("max_tokens_per_month")
  customLimits          Json     @default("{}")
  resetDate             DateTime @map("reset_date") @db.Date
  createdAt             DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt             DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, resetDate])
  @@index([tenantId])
  @@map("tenant_quotas")
}

model BillingInfo {
  id                String   @id @default(uuid()) @db.Uuid
  tenantId          String   @unique @map("tenant_id") @db.Uuid
  customerId        String?  @unique @map("customer_id") @db.VarChar(255)
  subscriptionId    String?  @unique @map("subscription_id") @db.VarChar(255)
  paymentMethodId   String?  @map("payment_method_id") @db.VarChar(255)
  billingEmail      String?  @map("billing_email") @db.VarChar(320)
  billingAddress    Json?    @map("billing_address")
  taxInfo           Json?    @map("tax_info")
  metadata          Json     @default("{}")
  createdAt         DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("billing_info")
}

model Subscription {
  id                String             @id @default(uuid()) @db.Uuid
  tenantId          String             @map("tenant_id") @db.Uuid
  planId            String             @map("plan_id") @db.VarChar(100)
  status            SubscriptionStatus @default(ACTIVE)
  currentPeriodStart DateTime          @map("current_period_start") @db.Timestamptz
  currentPeriodEnd   DateTime          @map("current_period_end") @db.Timestamptz
  trialStart        DateTime?          @map("trial_start") @db.Timestamptz
  trialEnd          DateTime?          @map("trial_end") @db.Timestamptz
  cancelledAt       DateTime?          @map("cancelled_at") @db.Timestamptz
  metadata          Json               @default("{}")
  createdAt         DateTime           @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime           @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([status])
  @@map("subscriptions")
}

model TenantInvitation {
  id          String                @id @default(uuid()) @db.Uuid
  tenantId    String                @map("tenant_id") @db.Uuid
  invitedBy   String                @map("invited_by") @db.Uuid
  email       String                @db.VarChar(320)
  role        UserRole              @default(USER)
  token       String                @unique @db.VarChar(255)
  status      TenantInvitationStatus @default(PENDING)
  expiresAt   DateTime              @map("expires_at") @db.Timestamptz
  acceptedAt  DateTime?             @map("accepted_at") @db.Timestamptz
  createdAt   DateTime              @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  invitedByUser User @relation(fields: [invitedBy], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([email])
  @@index([token])
  @@index([status])
  @@map("tenant_invitations")
}

model PasswordResetToken {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  token     String   @unique @db.VarChar(255)
  expiresAt DateTime @map("expires_at") @db.Timestamptz
  usedAt    DateTime? @map("used_at") @db.Timestamptz
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
  @@map("password_reset_tokens")
}

model EmailVerificationToken {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  token     String   @unique @db.VarChar(255)
  expiresAt DateTime @map("expires_at") @db.Timestamptz
  usedAt    DateTime? @map("used_at") @db.Timestamptz
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
  @@map("email_verification_tokens")
}

// ================================
// ENUMS
// ================================

enum TenantStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

enum UserRole {
  SUPER_ADMIN
  TENANT_ADMIN
  USER
  READONLY
}

enum UserStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

enum TenantUserStatus {
  ACTIVE
  SUSPENDED
  PENDING
}

enum AgentType {
  STANDALONE
  TOOL_DRIVEN
  HYBRID
  MULTI_TASKING
  MULTI_PROVIDER
}

enum AgentStatus {
  ACTIVE
  INACTIVE
  ERROR
}

enum ToolType {
  FUNCTION_CALLER
  RAG
  API_FETCHER
  BROWSER_AUTOMATION
  DB_RUNNER
  CUSTOM
}

enum ToolStatus {
  ACTIVE
  INACTIVE
  ERROR
}

enum WorkflowStatus {
  DRAFT
  ACTIVE
  PAUSED
  COMPLETED
  FAILED
}

enum WorkflowNodeType {
  AGENT
  TOOL
  CONDITION
  LOOP
  TRIGGER
  ACTION
  HUMAN_INPUT
}

enum SessionStatus {
  ACTIVE
  COMPLETED
  FAILED
  TIMEOUT
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
  TOOL
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  TIMEOUT
  CANCELLED
}

enum ProviderType {
  OPENAI
  CLAUDE
  GEMINI
  MISTRAL
  GROQ
  DEEPSEEK
  HUGGINGFACE
  OLLAMA
  LOCALAI
  CUSTOM
}

enum ProviderStatus {
  ACTIVE
  INACTIVE
  RATE_LIMITED
  ERROR
}

enum KnowledgeBaseStatus {
  ACTIVE
  INACTIVE
  PROCESSING
}

enum DocumentStatus {
  PROCESSING
  COMPLETED
  FAILED
}

enum ApiKeyStatus {
  ACTIVE
  INACTIVE
  EXPIRED
  REVOKED
}

enum AuditAction {
  CREATE
  READ
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  ACCESS_DENIED
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
  PAST_DUE
  TRIALING
}

enum TenantInvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  CANCELLED
}
