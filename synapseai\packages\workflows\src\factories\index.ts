import { z } from 'zod';
import { 
  Workflow, 
  WorkflowFactory, 
  WorkflowConfig, 
  WorkflowType,
  WorkflowConfigSchema
} from '../types';
import { 
  BaseWorkflowFactory, 
  LinearWorkflow, 
  ParallelWorkflow 
} from '../runtime';

// Linear Workflow Factory
export class LinearWorkflowFactory extends BaseWorkflowFactory {
  async create(config: WorkflowConfig): Promise<Workflow> {
    const validatedConfig = this.validate(config);
    return new LinearWorkflow(validatedConfig);
  }

  getDefaultConfig(): Partial<WorkflowConfig> {
    return {
      name: 'Linear Workflow',
      description: 'Execute nodes in sequential order',
      type: 'LINEAR',
      nodes: [
        {
          id: 'start',
          type: 'START',
          config: {},
          inputs: {},
          outputs: {},
        },
        {
          id: 'end',
          type: 'END',
          config: {},
          inputs: {},
          outputs: {},
        },
      ],
      edges: [
        {
          from: 'start',
          to: 'end',
        },
      ],
      triggers: [
        {
          type: 'MANUAL',
          config: {},
        },
      ],
      isActive: true,
    };
  }

  getSupportedTypes(): WorkflowType[] {
    return ['LINEAR'];
  }
}

// Parallel Workflow Factory
export class ParallelWorkflowFactory extends BaseWorkflowFactory {
  async create(config: WorkflowConfig): Promise<Workflow> {
    const validatedConfig = this.validate(config);
    return new ParallelWorkflow(validatedConfig);
  }

  getDefaultConfig(): Partial<WorkflowConfig> {
    return {
      name: 'Parallel Workflow',
      description: 'Execute nodes in parallel',
      type: 'PARALLEL',
      nodes: [
        {
          id: 'start',
          type: 'START',
          config: {},
          inputs: {},
          outputs: {},
        },
        {
          id: 'parallel1',
          type: 'PARALLEL',
          config: {},
          inputs: {},
          outputs: {},
        },
        {
          id: 'parallel2',
          type: 'PARALLEL',
          config: {},
          inputs: {},
          outputs: {},
        },
        {
          id: 'merge',
          type: 'MERGE',
          config: {},
          inputs: {},
          outputs: {},
        },
        {
          id: 'end',
          type: 'END',
          config: {},
          inputs: {},
          outputs: {},
        },
      ],
      edges: [
        { from: 'start', to: 'parallel1' },
        { from: 'start', to: 'parallel2' },
        { from: 'parallel1', to: 'merge' },
        { from: 'parallel2', to: 'merge' },
        { from: 'merge', to: 'end' },
      ],
      triggers: [
        {
          type: 'MANUAL',
          config: {},
        },
      ],
      isActive: true,
    };
  }

  getSupportedTypes(): WorkflowType[] {
    return ['PARALLEL'];
  }
}

// Workflow Factory Registry
export class WorkflowFactoryRegistry {
  private factories = new Map<WorkflowType, WorkflowFactory>();

  constructor() {
    // Register default factories
    this.register('LINEAR', new LinearWorkflowFactory());
    this.register('PARALLEL', new ParallelWorkflowFactory());
  }

  register(type: WorkflowType, factory: WorkflowFactory): void {
    this.factories.set(type, factory);
  }

  unregister(type: WorkflowType): void {
    this.factories.delete(type);
  }

  get(type: WorkflowType): WorkflowFactory | null {
    return this.factories.get(type) || null;
  }

  list(): WorkflowType[] {
    return Array.from(this.factories.keys());
  }

  async createWorkflow(type: WorkflowType, config: Partial<WorkflowConfig>): Promise<Workflow> {
    const factory = this.get(type);
    if (!factory) {
      throw new Error(`No factory registered for workflow type: ${type}`);
    }

    const defaultConfig = factory.getDefaultConfig();
    const fullConfig = { ...defaultConfig, ...config, type };
    
    return factory.create(factory.validate(fullConfig));
  }
}

// Default workflow factory registry instance
export const defaultWorkflowFactoryRegistry = new WorkflowFactoryRegistry();
