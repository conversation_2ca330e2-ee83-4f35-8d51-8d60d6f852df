import { z } from 'zod';
import { BaseEntitySchema, ExecutionStatusSchema } from '@synapseai/core';

// Tool Core Types
export const ToolTypeSchema = z.enum([
  'FUNCTION_CALLER',
  'RAG',
  'API_FETCHER',
  'BROWSER_AUTOMATION',
  'DATABASE_RUNNER',
  'CUSTOM_LOGIC',
  'FILE_PROCESSOR',
  'DATA_TRANSFORMER',
  'WEBHOOK',
  'SCHEDULER',
]);

export const ToolCategorySchema = z.enum([
  'DATA_PROCESSING',
  'API_INTEGRATION',
  'AUTOMATION',
  'ANALYSIS',
  'COMMUNICATION',
  'UTILITY',
  'STORAGE',
  'SECURITY',
  'MONITORING',
  'CUSTOM',
]);

export const ToolConfigSchema = BaseEntitySchema.extend({
  name: z.string().min(1),
  description: z.string(),
  type: ToolTypeSchema,
  category: ToolCategorySchema,
  version: z.string().default('1.0.0'),
  inputSchema: z.record(z.unknown()),
  outputSchema: z.record(z.unknown()),
  errorSchema: z.record(z.unknown()).optional(),
  timeout: z.number().positive().default(30000),
  retries: z.number().nonnegative().default(3),
  isAsync: z.boolean().default(false),
  requiresAuth: z.boolean().default(false),
  rateLimits: z.object({
    requestsPerMinute: z.number().positive().default(60),
    requestsPerHour: z.number().positive().default(1000),
  }).optional(),
  tags: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  metadata: z.record(z.unknown()).default({}),
});

export type ToolType = z.infer<typeof ToolTypeSchema>;
export type ToolCategory = z.infer<typeof ToolCategorySchema>;
export type ToolConfig = z.infer<typeof ToolConfigSchema>;

// Tool Input/Output Schemas
export const ToolInputSchema = z.object({
  toolId: z.string(),
  parameters: z.record(z.unknown()).default({}),
  context: z.record(z.unknown()).optional(),
  sessionId: z.string().optional(),
  userId: z.string().optional(),
  options: z.object({
    timeout: z.number().positive().optional(),
    retries: z.number().nonnegative().optional(),
    async: z.boolean().default(false),
  }).optional(),
});

export const ToolOutputSchema = z.object({
  id: z.string(),
  toolId: z.string(),
  result: z.unknown(),
  status: ExecutionStatusSchema,
  error: z.string().optional(),
  executionTime: z.number().nonnegative(),
  usage: z.object({
    apiCalls: z.number().nonnegative().default(0),
    dataProcessed: z.number().nonnegative().default(0),
    costIncurred: z.number().nonnegative().default(0),
  }).optional(),
  metadata: z.record(z.unknown()).default({}),
  timestamp: z.date(),
});

export type ToolInput = z.infer<typeof ToolInputSchema>;
export type ToolOutput = z.infer<typeof ToolOutputSchema>;

// Tool Execution Context
export const ToolExecutionContextSchema = z.object({
  toolId: z.string(),
  sessionId: z.string().optional(),
  userId: z.string().optional(),
  tenantId: z.string(),
  input: ToolInputSchema,
  config: ToolConfigSchema,
  startTime: z.date(),
  environment: z.record(z.unknown()).default({}),
});

export type ToolExecutionContext = z.infer<typeof ToolExecutionContextSchema>;

// Tool Plugin Interface
export const ToolPluginSchema = z.object({
  id: z.string(),
  name: z.string(),
  version: z.string(),
  description: z.string(),
  type: ToolTypeSchema,
  category: ToolCategorySchema,
  configSchema: z.record(z.unknown()),
  inputSchema: z.record(z.unknown()),
  outputSchema: z.record(z.unknown()),
  factory: z.function(),
  metadata: z.record(z.unknown()).default({}),
});

export type ToolPlugin = z.infer<typeof ToolPluginSchema>;

// Tool Factory Interface
export interface ToolFactory {
  create(config: ToolConfig): Promise<Tool>;
  validate(config: unknown): ToolConfig;
  getDefaultConfig(): Partial<ToolConfig>;
  getSupportedTypes(): ToolType[];
  testConnection?(): Promise<boolean>;
}

// Base Tool Interface
export interface Tool {
  readonly id: string;
  readonly config: ToolConfig;
  readonly type: ToolType;
  readonly category: ToolCategory;
  
  execute(input: ToolInput, context?: ToolExecutionContext): Promise<ToolOutput>;
  executeAsync?(input: ToolInput, context?: ToolExecutionContext): Promise<string>; // Returns execution ID
  getResult?(executionId: string): Promise<ToolOutput>;
  
  // Validation
  validateInput(input: unknown): ToolInput;
  validateOutput(output: unknown): ToolOutput;
  
  // Configuration
  updateConfig(updates: Partial<ToolConfig>): Promise<void>;
  
  // Health & Status
  healthCheck(): Promise<boolean>;
  getStatus(): ExecutionStatusSchema;
  getMetrics(): Promise<ToolMetrics>;
  
  // Lifecycle
  initialize(): Promise<void>;
  destroy(): Promise<void>;
}

// Tool Metrics
export const ToolMetricsSchema = z.object({
  totalExecutions: z.number().nonnegative(),
  successfulExecutions: z.number().nonnegative(),
  failedExecutions: z.number().nonnegative(),
  averageExecutionTime: z.number().nonnegative(),
  totalApiCalls: z.number().nonnegative(),
  totalDataProcessed: z.number().nonnegative(),
  totalCostIncurred: z.number().nonnegative(),
  lastExecutionAt: z.date().optional(),
  uptime: z.number().nonnegative(),
});

export type ToolMetrics = z.infer<typeof ToolMetricsSchema>;

// Tool Event Types
export const ToolEventSchema = z.enum([
  'TOOL_CREATED',
  'TOOL_STARTED',
  'TOOL_COMPLETED',
  'TOOL_FAILED',
  'TOOL_TIMEOUT',
  'TOOL_RETRYING',
  'TOOL_CANCELLED',
  'EXECUTION_START',
  'EXECUTION_PROGRESS',
  'EXECUTION_COMPLETE',
  'EXECUTION_ERROR',
  'DATA_PROCESSED',
  'API_CALL_MADE',
  'RATE_LIMIT_HIT',
]);

export type ToolEvent = z.infer<typeof ToolEventSchema>;

// Tool Registry Interface
export interface ToolRegistry {
  register(plugin: ToolPlugin): void;
  unregister(pluginId: string): void;
  get(pluginId: string): ToolPlugin | null;
  list(): ToolPlugin[];
  listByType(type: ToolType): ToolPlugin[];
  listByCategory(category: ToolCategory): ToolPlugin[];
  search(query: string): ToolPlugin[];
  createTool(pluginId: string, config: ToolConfig): Promise<Tool>;
}

// Tool Execution History
export const ToolExecutionHistorySchema = z.object({
  id: z.string(),
  toolId: z.string(),
  input: ToolInputSchema,
  output: ToolOutputSchema,
  context: ToolExecutionContextSchema,
  createdAt: z.date(),
});

export type ToolExecutionHistory = z.infer<typeof ToolExecutionHistorySchema>;

// Tool Template for common configurations
export const ToolTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  type: ToolTypeSchema,
  category: ToolCategorySchema,
  config: ToolConfigSchema,
  usage: z.string(),
  examples: z.array(z.object({
    name: z.string(),
    input: z.record(z.unknown()),
    expectedOutput: z.record(z.unknown()),
  })).default([]),
  tags: z.array(z.string()).default([]),
  isPublic: z.boolean().default(false),
  createdBy: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type ToolTemplate = z.infer<typeof ToolTemplateSchema>;
