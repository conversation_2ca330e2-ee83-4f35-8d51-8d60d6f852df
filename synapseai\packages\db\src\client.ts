// database client instantiation for SynapseAI with enhanced security and configurability
import { PrismaClient } from './generated'

const connectOptions = process.env.NODE_ENV === 'production' ? {
  log: ['error'],
} : {
  log: ['query', 'info', 'warn', 'error'],
}

// Ensure singleton in serverless context
const prisma = globalThis.prisma || new PrismaClient(connectOptions)
if (process.env.NODE_ENV !== 'production') globalThis.prisma = prisma

export const db = prisma

// Add Row-Level Security Context functions
export const withTenantId = (tenantId: string) => prisma.$use(async (params, next) => {
  return await prisma.$queryRaw`SET app.current_tenant_id=${tenantId}`.then(() => next(params))
})

export const withUserId = (userId: string) => prisma.$use(async (params, next) => {
  return await prisma.$queryRaw`SET app.current_user_id=${userId}`.then(() => next(params))
})

export const withUserRole = (userRole: string) => prisma.$use(async (params, next) => {
  return await prisma.$queryRaw`SET app.current_user_role=${userRole}`.then(() => next(params))
})
