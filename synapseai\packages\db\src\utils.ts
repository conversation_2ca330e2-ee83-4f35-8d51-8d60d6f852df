// Utilities related to database functionality

/**
 * Generates a secure random token
 * @param length The length of the token
 * @returns A base64 string token
 */
export async function generateSecureToken(length: number = 32): Promise<string> {
    const crypto = require('crypto')
    return crypto.randomBytes(length).toString('base64')
}

/**
 * Hashes a given login password using bcrypt
 * @param password Plain text password to be hashed
 * @returns Hashed password
 */
export async function hashPassword(password: string): Promise<string> {
    const bcrypt = require('bcryptjs')
    const saltRounds = 12
    return bcrypt.hash(password, saltRounds)
}

/**
 * Validates a given password with its hash
 * @param password Plain text password
 * @param hash Password hash
 * @returns <PERSON>olean indicating if valid
 */
export async function validatePassword(password: string, hash: string): Promise<boolean> {
    const bcrypt = require('bcryptjs')
    return bcrypt.compare(password, hash)
}
