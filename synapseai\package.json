{"name": "synapseai", "version": "0.1.0", "private": true, "type": "module", "description": "SynapseAI - Unified Production-Grade Multi-Agent AI Orchestration Platform", "author": "SynapseAI Team", "license": "UNLICENSED", "homepage": "https://github.com/synapseai/synapseai", "repository": {"type": "git", "url": "git+https://github.com/synapseai/synapseai.git"}, "bugs": {"url": "https://github.com/synapseai/synapseai/issues"}, "keywords": ["ai", "agents", "automation", "workflow", "orchestration", "real-time", "enterprise"], "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0"}, "packageManager": "pnpm@9.15.0", "workspaces": ["apps/*", "services/*", "packages/*"], "scripts": {"build": "turbo build", "dev": "turbo dev", "test": "turbo test", "test:e2e": "turbo test:e2e", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "format": "prettier --write \"**/*.{js,ts,tsx,json,md,yaml,yml}\"", "format:check": "prettier --check \"**/*.{js,ts,tsx,json,md,yaml,yml}\"", "typecheck": "turbo typecheck", "clean": "turbo clean && rm -rf node_modules", "prepare": "husky install", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo build --filter=!@synapseai/docs && changeset publish", "docker:build": "docker-compose -f infra/docker-compose.yml build", "docker:up": "docker-compose -f infra/docker-compose.yml up -d", "docker:down": "docker-compose -f infra/docker-compose.yml down", "db:migrate:ci": "turbo db:migrate:deploy", "db:seed:ci": "turbo db:seed", "db:generate": "turbo db:generate", "db:push": "turbo db:push", "db:migrate": "turbo db:migrate", "db:studio": "turbo db:studio"}, "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.8", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@types/node": "^22.8.4", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^9.1.6", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "turbo": "^2.2.3", "typescript": "^5.6.3", "vitest": "^2.1.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yaml,yml}": ["prettier --write"]}, "pnpm": {"overrides": {"@types/react": "^18.3.11", "@types/react-dom": "^18.3.1"}}}