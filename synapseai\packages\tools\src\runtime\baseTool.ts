import { z } from 'zod';
import { 
  Tool, 
  ToolFactory, 
  ToolConfig, 
  ToolInput, 
  ToolOutput, 
  ToolExecutionContext,
  ToolType,
  ToolCategory,
  ToolMetrics,
  ToolConfigSchema,
  ToolInputSchema,
  ToolOutputSchema,
  ToolMetricsSchema
} from '../types';
import { Logger, ExecutionStatus } from '@synapseai/core';

// Base Tool Implementation
export abstract class BaseTool implements Tool {
  public readonly id: string;
  public readonly config: ToolConfig;
  public readonly type: ToolType;
  public readonly category: ToolCategory;
  
  protected logger: Logger;
  protected metrics: ToolMetrics;
  protected startTime: Date;
  
  constructor(config: ToolConfig) {
    this.id = `tool_${Math.random().toString(36).substr(2, 9)}`;
    this.config = config;
    this.type = config.type;
    this.category = config.category;
    this.logger = new Logger();
    this.startTime = new Date();
    
    this.metrics = {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageExecutionTime: 0,
      totalApiCalls: 0,
      totalDataProcessed: 0,
      totalCostIncurred: 0,
      uptime: 0,
    };
  }

  // Abstract method to be implemented by subclasses
  protected abstract executeInternal(input: ToolInput, context?: ToolExecutionContext): Promise<unknown>;

  public async execute(input: ToolInput, context?: ToolExecutionContext): Promise<ToolOutput> {
    const startTime = Date.now();
    this.logger.info(`Executing tool ${this.config.name}`, { toolId: this.id, input });

    try {
      // Validate input
      const validatedInput = this.validateInput(input);
      
      // Update metrics
      this.metrics.totalExecutions++;
      
      // Execute the tool logic
      const result = await this.executeInternal(validatedInput, context);
      
      // Calculate execution time
      const executionTime = Date.now() - startTime;
      
      // Update success metrics
      this.metrics.successfulExecutions++;
      this.updateAverageExecutionTime(executionTime);
      
      // Create output
      const output: ToolOutput = {
        id: `output_${Math.random().toString(36).substr(2, 9)}`,
        toolId: this.id,
        result,
        status: 'COMPLETED',
        executionTime,
        timestamp: new Date(),
        metadata: {
          toolName: this.config.name,
          toolType: this.type,
          toolCategory: this.category,
        },
      };

      this.logger.info(`Tool execution completed`, { toolId: this.id, executionTime, output });
      return this.validateOutput(output);
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.metrics.failedExecutions++;
      this.updateAverageExecutionTime(executionTime);
      
      this.logger.error(`Tool execution failed`, { 
        toolId: this.id, 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      const output: ToolOutput = {
        id: `output_${Math.random().toString(36).substr(2, 9)}`,
        toolId: this.id,
        result: null,
        status: 'FAILED',
        error: error instanceof Error ? error.message : String(error),
        executionTime,
        timestamp: new Date(),
        metadata: {
          toolName: this.config.name,
          toolType: this.type,
          toolCategory: this.category,
        },
      };

      return this.validateOutput(output);
    }
  }

  public validateInput(input: unknown): ToolInput {
    return ToolInputSchema.parse(input);
  }

  public validateOutput(output: unknown): ToolOutput {
    return ToolOutputSchema.parse(output);
  }

  public async updateConfig(updates: Partial<ToolConfig>): Promise<void> {
    this.logger.info(`Updating tool configuration`, { toolId: this.id, updates });
    // Implementation would update the configuration
  }

  public async healthCheck(): Promise<boolean> {
    try {
      // Basic health check - can be overridden by subclasses
      return this.config.isActive;
    } catch (error) {
      this.logger.error(`Health check failed`, { toolId: this.id, error });
      return false;
    }
  }

  public getStatus(): ExecutionStatus {
    return this.config.isActive ? 'COMPLETED' : 'FAILED';
  }

  public async getMetrics(): Promise<ToolMetrics> {
    this.metrics.uptime = Date.now() - this.startTime.getTime();
    return ToolMetricsSchema.parse(this.metrics);
  }

  public async initialize(): Promise<void> {
    this.logger.info(`Initializing tool ${this.config.name}`, { toolId: this.id });
    // Override in subclasses for specific initialization logic
  }

  public async destroy(): Promise<void> {
    this.logger.info(`Destroying tool ${this.config.name}`, { toolId: this.id });
    // Override in subclasses for cleanup logic
  }

  private updateAverageExecutionTime(executionTime: number): void {
    const totalTime = this.metrics.averageExecutionTime * (this.metrics.totalExecutions - 1) + executionTime;
    this.metrics.averageExecutionTime = totalTime / this.metrics.totalExecutions;
  }
}

// Example Function Caller Tool
export class FunctionCallerTool extends BaseTool {
  protected async executeInternal(input: ToolInput, context?: ToolExecutionContext): Promise<unknown> {
    // Mock function calling logic
    const { parameters } = input;
    
    if (!parameters.functionName) {
      throw new Error('functionName parameter is required');
    }

    // Simulate function execution
    const result = {
      functionName: parameters.functionName,
      arguments: parameters.arguments || {},
      result: `Function ${parameters.functionName} executed successfully`,
      timestamp: new Date().toISOString(),
    };

    return result;
  }
}

// Example API Fetcher Tool
export class ApiFetcherTool extends BaseTool {
  protected async executeInternal(input: ToolInput, context?: ToolExecutionContext): Promise<unknown> {
    const { parameters } = input;
    
    if (!parameters.url) {
      throw new Error('url parameter is required');
    }

    // Mock API call
    const response = {
      url: parameters.url,
      method: parameters.method || 'GET',
      status: 200,
      data: { message: 'API call successful', timestamp: new Date().toISOString() },
      headers: { 'content-type': 'application/json' },
    };

    this.metrics.totalApiCalls++;
    return response;
  }
}

// Base Tool Factory Implementation
export abstract class BaseToolFactory implements ToolFactory {
  abstract create(config: ToolConfig): Promise<Tool>;
  
  validate(config: unknown): ToolConfig {
    return ToolConfigSchema.parse(config);
  }

  abstract getDefaultConfig(): Partial<ToolConfig>;
  abstract getSupportedTypes(): ToolType[];

  async testConnection(): Promise<boolean> {
    // Default implementation - override in subclasses
    return true;
  }
}
