import { z } from 'zod';

// SDK Configuration
export const SynapseClientConfigSchema = z.object({
  baseUrl: z.string().url(),
  apiKey: z.string().optional(),
  tenantId: z.string().optional(),
  timeout: z.number().positive().default(30000),
  retries: z.number().nonnegative().default(3),
  retryDelay: z.number().positive().default(1000),
  enableWebSocket: z.boolean().default(true),
  wsUrl: z.string().url().optional(),
  headers: z.record(z.string()).default({}),
});

export type SynapseClientConfig = z.infer<typeof SynapseClientConfigSchema>;

// HTTP Request/Response Types
export const HttpMethodSchema = z.enum(['GET', 'POST', 'PUT', 'PATCH', 'DELETE']);

export const HttpRequestSchema = z.object({
  method: HttpMethodSchema,
  url: z.string(),
  headers: z.record(z.string()).optional(),
  params: z.record(z.unknown()).optional(),
  data: z.unknown().optional(),
  timeout: z.number().positive().optional(),
});

export const HttpResponseSchema = z.object({
  status: z.number(),
  statusText: z.string(),
  headers: z.record(z.string()),
  data: z.unknown(),
  config: HttpRequestSchema,
});

export type HttpMethod = z.infer<typeof HttpMethodSchema>;
export type HttpRequest = z.infer<typeof HttpRequestSchema>;
export type HttpResponse = z.infer<typeof HttpResponseSchema>;

// WebSocket Event Types
export const WebSocketEventTypeSchema = z.enum([
  'connect',
  'disconnect',
  'message',
  'error',
  'reconnect',
  'reconnecting',
  'max_reconnects',
]);

export const WebSocketMessageSchema = z.object({
  id: z.string(),
  type: z.string(),
  channel: z.string(),
  data: z.unknown(),
  timestamp: z.date(),
  requestId: z.string().optional(),
});

export const WebSocketConnectionSchema = z.object({
  url: z.string().url(),
  protocols: z.array(z.string()).optional(),
  headers: z.record(z.string()).optional(),
  reconnect: z.boolean().default(true),
  maxReconnects: z.number().positive().default(5),
  reconnectInterval: z.number().positive().default(1000),
});

export type WebSocketEventType = z.infer<typeof WebSocketEventTypeSchema>;
export type WebSocketMessage = z.infer<typeof WebSocketMessageSchema>;
export type WebSocketConnection = z.infer<typeof WebSocketConnectionSchema>;

// API Response Types
export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    error: z.object({
      code: z.string(),
      message: z.string(),
      details: z.record(z.unknown()).optional(),
    }).optional(),
    meta: z.object({
      requestId: z.string(),
      timestamp: z.date(),
      version: z.string(),
    }),
  });

export type ApiResponse<T> = {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  meta: {
    requestId: string;
    timestamp: Date;
    version: string;
  };
};

// Client Event Types
export const ClientEventSchema = z.enum([
  'request:start',
  'request:success',
  'request:error',
  'websocket:connect',
  'websocket:disconnect',
  'websocket:message',
  'websocket:error',
]);

export type ClientEvent = z.infer<typeof ClientEventSchema>;

// SDK Plugin Interface
export const SdkPluginSchema = z.object({
  name: z.string(),
  version: z.string(),
  init: z.function().args(z.object({
    client: z.any(),
    config: SynapseClientConfigSchema,
  })).returns(z.void()),
  destroy: z.function().args().returns(z.void()).optional(),
});

export type SdkPlugin = z.infer<typeof SdkPluginSchema>;

// Interceptor Types
export type RequestInterceptor = (request: HttpRequest) => HttpRequest | Promise<HttpRequest>;
export type ResponseInterceptor = (response: HttpResponse) => HttpResponse | Promise<HttpResponse>;
export type ErrorInterceptor = (error: Error) => Error | Promise<Error>;

// Client State
export const ClientStateSchema = z.object({
  isConnected: z.boolean(),
  isReconnecting: z.boolean(),
  lastError: z.string().optional(),
  connectionCount: z.number().nonnegative(),
  requestCount: z.number().nonnegative(),
  errorCount: z.number().nonnegative(),
});

export type ClientState = z.infer<typeof ClientStateSchema>;
