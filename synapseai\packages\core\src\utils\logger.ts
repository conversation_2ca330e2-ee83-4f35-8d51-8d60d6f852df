import { z } from 'zod';
import { AuditLog, LogLevel, AuditLogSchema } from '../types';

// Logger Configuration
const LoggerConfigSchema = z.object({
  level: z.enum(['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL']).default('INFO'),
  transports: z.array(z.union([
    z.literal('console'),
    z.literal('file'),
    z.literal('http'),
    z.literal('remote')
  ])).default(['console']),
  format: z.enum(['json', 'text']).default('json'),
  enableColors: z.boolean().default(false),
});

export type LoggerConfig = z.infer<typeof LoggerConfigSchema>;

// Custom Logger
export class Logger {
  private config: LoggerConfig;

  constructor(config?: Partial<LoggerConfig>) {
    this.config = LoggerConfigSchema.parse(config || {});
  }

  public log(level: LogLevel, message: string, details: Record<string, unknown> = {}): void {
    const logEntry: AuditLog = {
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
      tenantId: 'SYSTEM',
      action: 'LOG',
      resourceType: 'LOGGER',
      resourceId: 'SYSTEM',
      level,
      details: { ...details, message },
    };
    this.outputLog(logEntry);
  }

  public debug(message: string, details?: Record<string, unknown>): void {
    this.log('DEBUG', message, details);
  }

  public info(message: string, details?: Record<string, unknown>): void {
    this.log('INFO', message, details);
  }

  public warn(message: string, details?: Record<string, unknown>): void {
    this.log('WARN', message, details);
  }

  public error(message: string, details?: Record<string, unknown>): void {
    this.log('ERROR', message, details);
  }

  public fatal(message: string, details?: Record<string, unknown>): void {
    this.log('FATAL', message, details);
  }

  private outputLog(log: AuditLog): void {
    // Log output logic based on config
    if (this.config.transports.includes('console')) {
      console.log(this.formatLog(log));
    }
    // Additional transport outputs (file, http, remote) can be implemented here
  }

  private formatLog(log: AuditLog): string {
    // Format the log based on the configured format
    return this.config.format === 'json'
      ? JSON.stringify(log, null, 2)
      : `[${log.level}] - ${log.createdAt.toISOString()} - ${log.details.message}`;
  }

  private generateId(): string {
    // Generate unique ID for audit log
    return `log_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const DefaultLogger = new Logger();
