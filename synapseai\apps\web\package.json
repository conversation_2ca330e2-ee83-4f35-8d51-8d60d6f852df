{"name": "@synapseai/web", "version": "0.1.0", "private": true, "type": "module", "description": "SynapseAI Web Application - Next.js 14 Dashboard", "scripts": {"build": "next build", "dev": "next dev --port 3000", "lint": "next lint", "lint:fix": "next lint --fix", "start": "next start", "typecheck": "tsc --noEmit", "clean": "rm -rf .next dist out", "test": "vitest", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-sheet": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@synapseai/core": "workspace:*", "@synapseai/db": "workspace:*", "@synapseai/sdk": "workspace:*", "@tanstack/react-query": "^5.59.0", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "framer-motion": "^11.11.17", "lucide-react": "^0.453.0", "next": "^15.1.2", "next-auth": "^4.24.10", "next-themes": "^0.4.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "recharts": "^2.12.7", "sonner": "^1.7.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^5.0.1"}, "devDependencies": {"@hookform/resolvers": "^3.9.1", "@next/eslint-config-next": "^15.1.2", "@playwright/test": "^1.49.0", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/nextjs": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/test": "^8.4.7", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "jsdom": "^25.0.1", "postcss": "^8.4.49", "storybook": "^8.4.7", "tailwindcss": "^3.4.17", "typescript": "^5.6.3", "vite": "^6.0.3", "vitest": "^2.1.9"}}