-- SynapseAI PostgreSQL Initialization Script
-- This script sets up essential extensions and database configuration

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Create custom types for SynapseAI
CREATE TYPE tenant_status AS ENUM ('active', 'suspended', 'deleted');
CREATE TYPE user_role AS ENUM ('super_admin', 'tenant_admin', 'user', 'readonly');
CREATE TYPE session_status AS ENUM ('active', 'completed', 'failed', 'timeout');
CREATE TYPE workflow_status AS ENUM ('draft', 'active', 'paused', 'completed', 'failed');
CREATE TYPE agent_type AS ENUM ('standalone', 'tool_driven', 'hybrid', 'multi_tasking', 'multi_provider');
CREATE TYPE tool_type AS ENUM ('function_caller', 'rag', 'api_fetcher', 'browser_automation', 'db_runner', 'custom');
CREATE TYPE provider_status AS ENUM ('active', 'inactive', 'rate_limited', 'error');
CREATE TYPE execution_status AS ENUM ('pending', 'running', 'completed', 'failed', 'timeout', 'cancelled');
CREATE TYPE audit_action AS ENUM ('create', 'read', 'update', 'delete', 'login', 'logout', 'access_denied');

-- Create functions for Row Level Security
CREATE OR REPLACE FUNCTION auth.current_tenant_id() RETURNS uuid AS $$
  SELECT nullif(current_setting('app.current_tenant_id', true), '')::uuid;
$$ LANGUAGE sql STABLE;

CREATE OR REPLACE FUNCTION auth.current_user_id() RETURNS uuid AS $$
  SELECT nullif(current_setting('app.current_user_id', true), '')::uuid;
$$ LANGUAGE sql STABLE;

CREATE OR REPLACE FUNCTION auth.current_user_role() RETURNS user_role AS $$
  SELECT nullif(current_setting('app.current_user_role', true), '')::user_role;
$$ LANGUAGE sql STABLE;

-- Function to check if user has access to tenant
CREATE OR REPLACE FUNCTION auth.has_tenant_access(tenant_id uuid) RETURNS boolean AS $$
BEGIN
  -- Super admins have access to all tenants
  IF auth.current_user_role() = 'super_admin' THEN
    RETURN true;
  END IF;
  
  -- Check if current tenant matches or user has explicit access
  RETURN tenant_id = auth.current_tenant_id() OR 
         EXISTS (
           SELECT 1 FROM tenant_users 
           WHERE user_id = auth.current_user_id() 
           AND tenant_id = has_tenant_access.tenant_id
           AND status = 'active'
         );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Function to generate secure tokens
CREATE OR REPLACE FUNCTION generate_secure_token(length int DEFAULT 32) RETURNS text AS $$
BEGIN
  RETURN encode(gen_random_bytes(length), 'base64');
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Function to hash passwords with bcrypt
CREATE OR REPLACE FUNCTION hash_password(password text) RETURNS text AS $$
BEGIN
  RETURN crypt(password, gen_salt('bf', 12));
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Function to verify passwords
CREATE OR REPLACE FUNCTION verify_password(password text, hash text) RETURNS boolean AS $$
BEGIN
  RETURN hash = crypt(password, hash);
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create audit log entries
CREATE OR REPLACE FUNCTION create_audit_log() RETURNS TRIGGER AS $$
DECLARE
  old_data jsonb;
  new_data jsonb;
  action_type audit_action;
BEGIN
  -- Determine action type
  IF TG_OP = 'INSERT' THEN
    action_type := 'create';
    old_data := NULL;
    new_data := to_jsonb(NEW);
  ELSIF TG_OP = 'UPDATE' THEN
    action_type := 'update';
    old_data := to_jsonb(OLD);
    new_data := to_jsonb(NEW);
  ELSIF TG_OP = 'DELETE' THEN
    action_type := 'delete';
    old_data := to_jsonb(OLD);
    new_data := NULL;
  END IF;

  -- Insert audit log (will be created by Prisma migrations)
  INSERT INTO audit_logs (
    id,
    tenant_id,
    user_id,
    action,
    table_name,
    record_id,
    old_data,
    new_data,
    ip_address,
    user_agent,
    created_at
  ) VALUES (
    gen_random_uuid(),
    COALESCE(auth.current_tenant_id(), 
             CASE WHEN TG_OP = 'DELETE' THEN (OLD ->> 'tenant_id')::uuid 
                  ELSE (NEW ->> 'tenant_id')::uuid END),
    auth.current_user_id(),
    action_type,
    TG_TABLE_NAME,
    CASE WHEN TG_OP = 'DELETE' THEN (OLD ->> 'id')::uuid 
         ELSE (NEW ->> 'id')::uuid END,
    old_data,
    new_data,
    current_setting('app.client_ip', true),
    current_setting('app.user_agent', true),
    CURRENT_TIMESTAMP
  );

  RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create search configuration for full-text search
CREATE TEXT SEARCH CONFIGURATION synapseai (PARSER = pg_catalog."default");

-- Add mappings for text search
ALTER TEXT SEARCH CONFIGURATION synapseai
    ADD MAPPING FOR asciiword, asciihword, hword_asciipart,
                    word, hword, hword_part
    WITH unaccent, simple;
