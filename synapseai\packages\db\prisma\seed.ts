#!/usr/bin/env tsx

import { PrismaClient } from '../src/generated'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting SynapseAI database seeding...')

  // Create system tenant for super admin operations
  console.log('Creating system tenant...')
  const systemTenant = await prisma.tenant.upsert({
    where: { slug: 'system' },
    update: {},
    create: {
      id: '00000000-0000-0000-0000-000000000000',
      name: 'System',
      slug: 'system',
      status: 'ACTIVE',
      settings: {
        isSystemTenant: true,
        allowSignup: false,
        enforceEmailVerification: true,
      },
      metadata: {
        description: 'System tenant for administrative operations',
        version: '1.0.0',
      },
    },
  })

  // Create super admin user
  console.log('Creating super admin user...')
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: await hash('admin123!', 12), // Change in production
      firstName: 'System',
      lastName: 'Administrator',
      role: 'SUPER_ADMIN',
      status: 'ACTIVE',
      emailVerifiedAt: new Date(),
      tenantId: systemTenant.id,
      settings: {
        theme: 'dark',
        language: 'en',
        timezone: 'UTC',
        notifications: {
          email: true,
          browser: true,
          digest: 'weekly',
        },
      },
      metadata: {
        isSystemUser: true,
        createdBy: 'system-seed',
      },
    },
  })

  // Create system quotas for the system tenant
  console.log('Creating system tenant quotas...')
  await prisma.tenantQuota.upsert({
    where: {
      tenantId_resetDate: {
        tenantId: systemTenant.id,
        resetDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      },
    },
    update: {},
    create: {
      tenantId: systemTenant.id,
      maxUsers: 1000,
      maxAgents: 1000,
      maxTools: 1000,
      maxWorkflows: 1000,
      maxSessions: 10000,
      maxDocuments: 10000,
      maxStorageBytes: BigInt(100 * 1024 * 1024 * 1024), // 100GB
      maxApiCallsPerMonth: 1000000,
      maxTokensPerMonth: BigInt(10000000),
      resetDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      customLimits: {
        unlimited: true,
        description: 'System tenant has unlimited resources',
      },
    },
  })

  // Create demo tenant for development/testing
  console.log('Creating demo tenant...')
  const demoTenant = await prisma.tenant.upsert({
    where: { slug: 'demo' },
    update: {},
    create: {
      name: 'Demo Organization',
      slug: 'demo',
      domain: 'demo.synapseai.dev',
      status: 'ACTIVE',
      settings: {
        allowSignup: true,
        enforceEmailVerification: false,
        brandColor: '#3B82F6',
        logoUrl: '/logo/demo.png',
        features: {
          agents: true,
          tools: true,
          workflows: true,
          knowledge: true,
          analytics: true,
        },
      },
      metadata: {
        description: 'Demo tenant for testing and development',
        type: 'demo',
        version: '1.0.0',
      },
    },
  })

  // Create demo admin user
  console.log('Creating demo admin user...')
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: await hash('demo123!', 12), // Change in production
      firstName: 'Demo',
      lastName: 'Admin',
      role: 'TENANT_ADMIN',
      status: 'ACTIVE',
      emailVerifiedAt: new Date(),
      tenantId: demoTenant.id,
      settings: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        notifications: {
          email: true,
          browser: true,
          digest: 'daily',
        },
      },
      metadata: {
        isDemoUser: true,
        createdBy: 'system-seed',
      },
    },
  })

  // Create demo tenant quotas
  console.log('Creating demo tenant quotas...')
  await prisma.tenantQuota.upsert({
    where: {
      tenantId_resetDate: {
        tenantId: demoTenant.id,
        resetDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      },
    },
    update: {},
    create: {
      tenantId: demoTenant.id,
      maxUsers: 10,
      maxAgents: 5,
      maxTools: 20,
      maxWorkflows: 10,
      maxSessions: 100,
      maxDocuments: 1000,
      maxStorageBytes: BigInt(1024 * 1024 * 1024), // 1GB
      maxApiCallsPerMonth: 10000,
      maxTokensPerMonth: BigInt(1000000),
      resetDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      customLimits: {
        isDemoTenant: true,
        description: 'Standard demo tenant limits',
      },
    },
  })

  // Set up default provider configurations for demo tenant
  console.log('Creating default provider configurations...')
  
  const defaultProviders = [
    {
      name: 'OpenAI GPT-4',
      type: 'OPENAI' as const,
      capabilities: ['chat', 'completion', 'function_calling', 'vision'],
      priority: 1,
      costConfig: {
        inputTokenCost: 0.03,
        outputTokenCost: 0.06,
        currency: 'USD',
        unit: '1K tokens',
      },
      settings: {
        model: 'gpt-4-turbo-preview',
        maxTokens: 4096,
        temperature: 0.7,
        topP: 1,
        frequencyPenalty: 0,
        presencePenalty: 0,
      },
    },
    {
      name: 'Claude 3 Sonnet',
      type: 'CLAUDE' as const,
      capabilities: ['chat', 'completion', 'function_calling', 'vision'],
      priority: 2,
      costConfig: {
        inputTokenCost: 0.003,
        outputTokenCost: 0.015,
        currency: 'USD',
        unit: '1K tokens',
      },
      settings: {
        model: 'claude-3-sonnet-20240229',
        maxTokens: 4096,
        temperature: 0.7,
      },
    },
    {
      name: 'Gemini Pro',
      type: 'GEMINI' as const,
      capabilities: ['chat', 'completion', 'vision'],
      priority: 3,
      costConfig: {
        inputTokenCost: 0.0005,
        outputTokenCost: 0.0015,
        currency: 'USD',
        unit: '1K tokens',
      },
      settings: {
        model: 'gemini-pro',
        maxTokens: 2048,
        temperature: 0.7,
      },
    },
  ]

  for (const provider of defaultProviders) {
    await prisma.providerConfig.upsert({
      where: {
        tenantId_name: {
          tenantId: demoTenant.id,
          name: provider.name,
        },
      },
      update: {},
      create: {
        tenantId: demoTenant.id,
        name: provider.name,
        type: provider.type,
        status: 'INACTIVE', // Requires API keys to be activated
        capabilities: provider.capabilities,
        priority: provider.priority,
        costConfig: provider.costConfig,
        settings: provider.settings,
        quotas: {
          maxRequestsPerMinute: 60,
          maxRequestsPerDay: 10000,
          maxTokensPerDay: 100000,
        },
        metadata: {
          isDefault: true,
          description: `Default ${provider.name} configuration`,
        },
      },
    })
  }

  // Create default knowledge base for demo tenant
  console.log('Creating default knowledge base...')
  await prisma.knowledgeBase.upsert({
    where: {
      tenantId_name: {
        tenantId: demoTenant.id,
        name: 'Getting Started',
      },
    },
    update: {},
    create: {
      tenantId: demoTenant.id,
      name: 'Getting Started',
      description: 'Default knowledge base with getting started information',
      status: 'ACTIVE',
      config: {
        embeddingModel: 'text-embedding-ada-002',
        chunkSize: 1000,
        chunkOverlap: 200,
        maxChunks: 10000,
      },
      settings: {
        isDefault: true,
        allowPublicAccess: false,
        requireAuthentication: true,
      },
      metadata: {
        version: '1.0.0',
        createdBy: 'system-seed',
      },
    },
  })

  console.log('✅ Database seeding completed successfully!')
  console.log('📊 Seeded data summary:')
  console.log('  - System tenant with super admin user')
  console.log('  - Demo tenant with admin user')
  console.log('  - Default provider configurations')
  console.log('  - Default knowledge base')
  console.log('  - Tenant quotas and limits')
  console.log('')
  console.log('🔐 Default credentials:')
  console.log('  Super Admin: <EMAIL> / admin123!')
  console.log('  Demo Admin:  <EMAIL> / demo123!')
  console.log('')
  console.log('⚠️  Remember to change default passwords in production!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
