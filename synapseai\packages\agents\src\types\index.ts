import { z } from 'zod';
import { BaseEntitySchema, SessionSchema, ProviderTypeSchema, ExecutionStatusSchema } from '@synapseai/core';

// Agent Core Types
export const AgentTypeSchema = z.enum([
  'STANDALONE',
  'TOOL_DRIVEN',
  'HYBRID',
  'MULTI_TASKING',
  'MULTI_PROVIDER',
  'CONVERSATIONAL',
  'AUTONOMOUS',
]);

export const AgentCapabilitySchema = z.enum([
  'CHAT',
  'TOOL_EXECUTION',
  'WORKFLOW_ORCHESTRATION',
  'MEMORY_MANAGEMENT',
  'MULTI_MODAL',
  'CODE_GENERATION',
  'FUNCTION_CALLING',
  'RAG',
  'REAL_TIME',
]);

export const AgentConfigSchema = BaseEntitySchema.extend({
  name: z.string().min(1),
  description: z.string().optional(),
  type: AgentTypeSchema,
  capabilities: z.array(AgentCapabilitySchema),
  provider: ProviderTypeSchema,
  model: z.string(),
  systemPrompt: z.string().optional(),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().positive().default(4000),
  topP: z.number().min(0).max(1).default(1),
  frequencyPenalty: z.number().min(-2).max(2).default(0),
  presencePenalty: z.number().min(-2).max(2).default(0),
  stopSequences: z.array(z.string()).default([]),
  tools: z.array(z.string()).default([]),
  memory: z.object({
    enabled: z.boolean().default(true),
    maxSize: z.number().positive().default(10000),
    strategy: z.enum(['FIFO', 'LIFO', 'PRIORITY']).default('FIFO'),
    persistence: z.boolean().default(true),
  }),
  isActive: z.boolean().default(true),
  metadata: z.record(z.unknown()).default({}),
});

export type AgentType = z.infer<typeof AgentTypeSchema>;
export type AgentCapability = z.infer<typeof AgentCapabilitySchema>;
export type AgentConfig = z.infer<typeof AgentConfigSchema>;

// Agent Input/Output Schemas
export const AgentInputSchema = z.object({
  message: z.string(),
  context: z.record(z.unknown()).optional(),
  sessionId: z.string().optional(),
  tools: z.array(z.string()).optional(),
  options: z.object({
    stream: z.boolean().default(false),
    maxTokens: z.number().positive().optional(),
    temperature: z.number().min(0).max(2).optional(),
    stopSequences: z.array(z.string()).optional(),
  }).optional(),
});

export const AgentOutputSchema = z.object({
  id: z.string(),
  message: z.string(),
  usage: z.object({
    promptTokens: z.number().nonnegative(),
    completionTokens: z.number().nonnegative(),
    totalTokens: z.number().nonnegative(),
  }),
  toolCalls: z.array(z.object({
    id: z.string(),
    name: z.string(),
    arguments: z.record(z.unknown()),
    result: z.unknown().optional(),
  })).default([]),
  context: z.record(z.unknown()).default({}),
  sessionId: z.string(),
  executionTime: z.number().nonnegative(),
  metadata: z.record(z.unknown()).default({}),
});

export type AgentInput = z.infer<typeof AgentInputSchema>;
export type AgentOutput = z.infer<typeof AgentOutputSchema>;

// Agent Session & Memory
export const AgentSessionSchema = SessionSchema.extend({
  agentId: z.string(),
  conversationHistory: z.array(z.object({
    role: z.enum(['user', 'assistant', 'system', 'tool']),
    content: z.string(),
    timestamp: z.date(),
    toolCalls: z.array(z.object({
      id: z.string(),
      name: z.string(),
      arguments: z.record(z.unknown()),
      result: z.unknown().optional(),
    })).optional(),
  })).default([]),
  toolsUsed: z.array(z.string()).default([]),
  preferences: z.record(z.unknown()).default({}),
});

export type AgentSession = z.infer<typeof AgentSessionSchema>;

// Agent Execution Context
export const AgentExecutionContextSchema = z.object({
  agentId: z.string(),
  sessionId: z.string(),
  userId: z.string().optional(),
  tenantId: z.string(),
  input: AgentInputSchema,
  config: AgentConfigSchema,
  tools: z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    schema: z.record(z.unknown()),
    execute: z.function(),
  })).default([]),
  startTime: z.date(),
});

export type AgentExecutionContext = z.infer<typeof AgentExecutionContextSchema>;

// Agent Plugin Interface
export const AgentPluginSchema = z.object({
  id: z.string(),
  name: z.string(),
  version: z.string(),
  description: z.string(),
  type: AgentTypeSchema,
  capabilities: z.array(AgentCapabilitySchema),
  configSchema: z.record(z.unknown()),
  inputSchema: z.record(z.unknown()),
  outputSchema: z.record(z.unknown()),
  factory: z.function(),
  metadata: z.record(z.unknown()).default({}),
});

export type AgentPlugin = z.infer<typeof AgentPluginSchema>;

// Agent Factory Interface
export interface AgentFactory {
  create(config: AgentConfig): Promise<Agent>;
  validate(config: unknown): AgentConfig;
  getDefaultConfig(): Partial<AgentConfig>;
  getSupportedCapabilities(): AgentCapability[];
}

// Base Agent Interface
export interface Agent {
  readonly id: string;
  readonly config: AgentConfig;
  
  execute(input: AgentInput, context?: AgentExecutionContext): Promise<AgentOutput>;
  stream?(input: AgentInput, context?: AgentExecutionContext): AsyncGenerator<Partial<AgentOutput>, AgentOutput, unknown>;
  
  // Session management
  createSession(userId?: string): Promise<AgentSession>;
  getSession(sessionId: string): Promise<AgentSession | null>;
  updateSession(sessionId: string, updates: Partial<AgentSession>): Promise<AgentSession>;
  endSession(sessionId: string): Promise<void>;
  
  // Memory management
  getMemory(sessionId: string): Promise<Record<string, unknown>>;
  setMemory(sessionId: string, key: string, value: unknown): Promise<void>;
  clearMemory(sessionId: string): Promise<void>;
  
  // Tool management
  addTool(toolId: string): Promise<void>;
  removeTool(toolId: string): Promise<void>;
  listTools(): string[];
  
  // Configuration
  updateConfig(updates: Partial<AgentConfig>): Promise<void>;
  
  // State management
  pause(): Promise<void>;
  resume(): Promise<void>;
  stop(): Promise<void>;
  getStatus(): ExecutionStatusSchema;
  
  // Cleanup
  destroy(): Promise<void>;
}

// Agent Event Types
export const AgentEventSchema = z.enum([
  'AGENT_CREATED',
  'AGENT_STARTED',
  'AGENT_COMPLETED',
  'AGENT_FAILED',
  'AGENT_PAUSED',
  'AGENT_RESUMED',
  'AGENT_STOPPED',
  'TOOL_CALL_START',
  'TOOL_CALL_RESULT',
  'TOOL_CALL_ERROR',
  'MEMORY_UPDATED',
  'SESSION_CREATED',
  'SESSION_ENDED',
  'THINKING_START',
  'THINKING_END',
  'STREAM_CHUNK',
]);

export type AgentEvent = z.infer<typeof AgentEventSchema>;

// Agent Registry Interface
export interface AgentRegistry {
  register(plugin: AgentPlugin): void;
  unregister(pluginId: string): void;
  get(pluginId: string): AgentPlugin | null;
  list(): AgentPlugin[];
  listByType(type: AgentType): AgentPlugin[];
  listByCapability(capability: AgentCapability): AgentPlugin[];
  createAgent(pluginId: string, config: AgentConfig): Promise<Agent>;
}
