// Shared event types for APIX Protocol
import { z } from 'zod';

// Channels
export const ApixChannelSchema = z.enum([
  'agent-events',
  'tool-events',
  'workflow-events',
  'provider-events',
  'system-events',
]);

// Event Types
export const ApixEventTypeSchema = z.enum([
  'tool_call_start',
  'tool_call_result',
  'tool_call_error',
  'thinking_status',
  'text_chunk',
  'state_update',
  'request_user_input',
  'session_start',
  'session_end',
  'error_occurred',
  'fallback_triggered',
]);

// Base Event Structure
export const ApixBaseEventSchema = z.object({
  id: z.string(),
  type: ApixEventTypeSchema,
  channel: ApixChannelSchema,
  timestamp: z.date(),
  payload: z.record(z.unknown()),
  metadata: z.record(z.unknown()).optional(),
});

export type ApixChannel = z.infer<typeof ApixChannelSchema>;
export type ApixEventType = z.infer<typeof ApixEventTypeSchema>;
export type ApixBaseEvent = z.infer<typeof ApixBaseEventSchema>;

// Specific Event Types and Payloads
export const ToolCallStartEventSchema = ApixBaseEventSchema.extend({
  payload: z.object({
    toolId: z.string(),
    input: z.record(z.unknown()),
    sessionId: z.string(),
  }),
});

export const ToolCallResultEventSchema = ApixBaseEventSchema.extend({
  payload: z.object({
    toolId: z.string(),
    output: z.record(z.unknown()),
    sessionId: z.string(),
  }),
});

export const ToolCallErrorEventSchema = ApixBaseEventSchema.extend({
  payload: z.object({
    toolId: z.string(),
    error: z.string(),
    sessionId: z.string(),
  }),
});

// Export specific event types
export type ToolCallStartEvent = z.infer<typeof ToolCallStartEventSchema>;
export type ToolCallResultEvent = z.infer<typeof ToolCallResultEventSchema>;
export type ToolCallErrorEvent = z.infer<typeof ToolCallErrorEventSchema>;
