{"name": "@synapseai/agents", "version": "0.1.0", "type": "module", "description": "SynapseAI Agents - Runtime Agent Plugins and Factories", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./factories": {"types": "./dist/factories/index.d.ts", "import": "./dist/factories/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js"}, "./runtime": {"types": "./dist/runtime/index.d.ts", "import": "./dist/runtime/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && tsc-alias", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@synapseai/core": "workspace:*", "@ai-sdk/anthropic": "^1.0.5", "@ai-sdk/google": "^1.0.8", "@ai-sdk/mistral": "^1.0.4", "@ai-sdk/openai": "^1.0.8", "ai": "^4.0.38", "nanoid": "^5.0.9", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.8.4", "tsc-alias": "^1.8.10", "typescript": "^5.6.3", "vitest": "^2.1.9"}, "peerDependencies": {"@synapseai/db": "workspace:*"}}