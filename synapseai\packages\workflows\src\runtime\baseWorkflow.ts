import { EventEmitter } from 'events';
import { 
  Workflow, 
  WorkflowFactory, 
  WorkflowConfig, 
  WorkflowType,
  WorkflowExecution,
  WorkflowExecutionContext,
  WorkflowEvent,
  WorkflowConfigSchema
} from '../types';
import { Logger, ExecutionStatus } from '@synapseai/core';

// Base Workflow Implementation
export abstract class BaseWorkflow extends EventEmitter implements Workflow {
  public readonly id: string;
  public readonly config: WorkflowConfig;
  public readonly type: WorkflowType;
  
  protected logger: Logger;
  protected status: ExecutionStatus = 'PENDING';
  
  constructor(config: WorkflowConfig) {
    super();
    this.id = `workflow_${Math.random().toString(36).substr(2, 9)}`;
    this.config = config;
    this.type = config.type;
    this.logger = new Logger();
  }

  // Abstract method to be implemented by subclasses
  protected abstract executeInternal(context: WorkflowExecutionContext): Promise<WorkflowExecution>;

  public async execute(context: WorkflowExecutionContext): Promise<WorkflowExecution> {
    const startTime = Date.now();
    this.logger.info(`Starting workflow execution`, { workflowId: this.id, context });
    
    try {
      this.status = 'RUNNING';
      this.emit('WORKFLOW_STARTED', { workflowId: this.id, context });
      
      const execution = await this.executeInternal(context);
      
      const duration = Date.now() - startTime;
      const finalExecution: WorkflowExecution = {
        ...execution,
        duration,
        status: 'COMPLETED',
      };
      
      this.status = 'COMPLETED';
      this.emit('WORKFLOW_COMPLETED', finalExecution);
      this.logger.info(`Workflow execution completed`, { workflowId: this.id, duration });
      
      return finalExecution;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.status = 'FAILED';
      
      const errorExecution: WorkflowExecution = {
        id: `exec_${Math.random().toString(36).substr(2, 9)}`,
        workflowId: this.id,
        status: 'FAILED',
        startTime: new Date(startTime),
        duration,
        error: error instanceof Error ? error.message : String(error),
        metadata: {},
      };
      
      this.emit('WORKFLOW_FAILED', errorExecution);
      this.logger.error(`Workflow execution failed`, { 
        workflowId: this.id, 
        error: error instanceof Error ? error.message : String(error)
      });
      
      return errorExecution;
    }
  }

  public async pause(): Promise<void> {
    this.status = 'PAUSED';
    this.emit('WORKFLOW_PAUSED', { workflowId: this.id });
    this.logger.info(`Workflow paused`, { workflowId: this.id });
  }

  public async resume(): Promise<void> {
    this.status = 'RUNNING';
    this.emit('WORKFLOW_RESUMED', { workflowId: this.id });
    this.logger.info(`Workflow resumed`, { workflowId: this.id });
  }

  public async cancel(): Promise<void> {
    this.status = 'CANCELLED';
    this.emit('WORKFLOW_CANCELLED', { workflowId: this.id });
    this.logger.info(`Workflow cancelled`, { workflowId: this.id });
  }

  public validateConfig(config: unknown): WorkflowConfig {
    return WorkflowConfigSchema.parse(config);
  }
}

// Linear Workflow Implementation
export class LinearWorkflow extends BaseWorkflow {
  protected async executeInternal(context: WorkflowExecutionContext): Promise<WorkflowExecution> {
    const execution: WorkflowExecution = {
      id: `exec_${Math.random().toString(36).substr(2, 9)}`,
      workflowId: this.id,
      status: 'RUNNING',
      startTime: context.startTime,
      metadata: {
        workflowType: this.type,
        totalNodes: this.config.nodes.length,
      },
    };

    // Execute nodes in linear sequence
    for (const node of this.config.nodes) {
      this.logger.info(`Executing node`, { nodeId: node.id, nodeType: node.type });
      this.emit('NODE_STARTED', { nodeId: node.id, workflowId: this.id });
      
      // Mock node execution
      await this.sleep(100); // Simulate processing time
      
      this.emit('NODE_COMPLETED', { nodeId: node.id, workflowId: this.id });
      this.logger.info(`Node completed`, { nodeId: node.id });
    }

    execution.result = {
      executedNodes: this.config.nodes.length,
      success: true,
    };

    return execution;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Parallel Workflow Implementation
export class ParallelWorkflow extends BaseWorkflow {
  protected async executeInternal(context: WorkflowExecutionContext): Promise<WorkflowExecution> {
    const execution: WorkflowExecution = {
      id: `exec_${Math.random().toString(36).substr(2, 9)}`,
      workflowId: this.id,
      status: 'RUNNING',
      startTime: context.startTime,
      metadata: {
        workflowType: this.type,
        totalNodes: this.config.nodes.length,
      },
    };

    // Execute all nodes in parallel
    const nodePromises = this.config.nodes.map(async (node) => {
      this.logger.info(`Executing node in parallel`, { nodeId: node.id, nodeType: node.type });
      this.emit('NODE_STARTED', { nodeId: node.id, workflowId: this.id });
      
      // Mock node execution
      await this.sleep(Math.random() * 200); // Random processing time
      
      this.emit('NODE_COMPLETED', { nodeId: node.id, workflowId: this.id });
      this.logger.info(`Node completed in parallel`, { nodeId: node.id });
      
      return { nodeId: node.id, success: true };
    });

    const results = await Promise.all(nodePromises);
    
    execution.result = {
      executedNodes: results.length,
      nodeResults: results,
      success: true,
    };

    return execution;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Base Workflow Factory Implementation
export abstract class BaseWorkflowFactory implements WorkflowFactory {
  abstract create(config: WorkflowConfig): Promise<Workflow>;
  
  validate(config: unknown): WorkflowConfig {
    return WorkflowConfigSchema.parse(config);
  }

  abstract getDefaultConfig(): Partial<WorkflowConfig>;
  abstract getSupportedTypes(): WorkflowType[];
}
