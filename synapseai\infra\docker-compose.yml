version: '3.8'

services:
  # PostgreSQL with pgvector extension
  postgres:
    image: pgvector/pgvector:pg16
    container_name: synapseai-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: synapseai
      POSTGRES_USER: synapseai_user
      POSTGRES_PASSWORD: synapseai_dev_password
      POSTGRES_INITDB_ARGS: '--auth-host=scram-sha-256 --auth-local=scram-sha-256'
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
      - ./postgres/config/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    command: >
      postgres 
      -c config_file=/etc/postgresql/postgresql.conf
      -c shared_preload_libraries=vector
      -c log_statement=all
      -c log_destination=stderr
      -c logging_collector=on
      -c log_directory=/var/log/postgresql
      -c log_filename=postgresql.log
      -c log_rotation_age=1d
      -c log_rotation_size=100MB
    networks:
      - synapseai-network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U synapseai_user -d synapseai']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Cluster for session and memory management
  redis-master:
    image: redis:7.2-alpine
    container_name: synapseai-redis-master
    restart: unless-stopped
    environment:
      REDIS_REPLICATION_MODE: master
      REDIS_PASSWORD: synapseai_redis_password
    ports:
      - '6379:6379'
    volumes:
      - redis_master_data:/data
      - ./redis/config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: >
      redis-server /usr/local/etc/redis/redis.conf
      --requirepass synapseai_redis_password
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    networks:
      - synapseai-network
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  redis-replica:
    image: redis:7.2-alpine
    container_name: synapseai-redis-replica
    restart: unless-stopped
    environment:
      REDIS_REPLICATION_MODE: slave
      REDIS_MASTER_HOST: redis-master
      REDIS_MASTER_PORT_NUMBER: 6379
      REDIS_MASTER_PASSWORD: synapseai_redis_password
      REDIS_PASSWORD: synapseai_redis_password
    ports:
      - '6380:6379'
    volumes:
      - redis_replica_data:/data
      - ./redis/config/redis-replica.conf:/usr/local/etc/redis/redis.conf:ro
    command: >
      redis-server /usr/local/etc/redis/redis.conf
      --replicaof redis-master 6379
      --masterauth synapseai_redis_password
      --requirepass synapseai_redis_password
      --appendonly yes
    depends_on:
      redis-master:
        condition: service_healthy
    networks:
      - synapseai-network
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # Redis Sentinel for high availability
  redis-sentinel:
    image: redis:7.2-alpine
    container_name: synapseai-redis-sentinel
    restart: unless-stopped
    ports:
      - '26379:26379'
    volumes:
      - ./redis/config/sentinel.conf:/usr/local/etc/redis/sentinel.conf:ro
    command: >
      redis-sentinel /usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis-master
      - redis-replica
    networks:
      - synapseai-network

  # PostgreSQL Admin (pgAdmin)
  pgadmin:
    image: dpage/pgadmin4:8
    container_name: synapseai-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: synapseai_admin_password
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - '5050:80'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./pgadmin/servers.json:/pgadmin4/servers.json:ro
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - synapseai-network

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: synapseai-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: 'master:redis-master:6379:0:synapseai_redis_password,replica:redis-replica:6379:0:synapseai_redis_password'
      HTTP_USER: admin
      HTTP_PASSWORD: synapseai_admin_password
    ports:
      - '8081:8081'
    depends_on:
      redis-master:
        condition: service_healthy
    networks:
      - synapseai-network

volumes:
  postgres_data:
    driver: local
  redis_master_data:
    driver: local
  redis_replica_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  synapseai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
