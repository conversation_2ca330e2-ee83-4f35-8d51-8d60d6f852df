{"name": "@synapseai/events", "version": "0.1.0", "type": "module", "description": "SynapseAI Events - Shared Event Types for APIX Protocol", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js"}, "./constants": {"types": "./dist/constants/index.d.ts", "import": "./dist/constants/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && tsc-alias", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@synapseai/core": "workspace:*", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.8.4", "tsc-alias": "^1.8.10", "typescript": "^5.6.3", "vitest": "^2.1.9"}}