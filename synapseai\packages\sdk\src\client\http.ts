import { 
  SynapseClientConfig, 
  HttpRequest, 
  HttpResponse,
  HttpMethod 
} from '../types';

export class HttpClient {
  private config: SynapseClientConfig;

  constructor(config: SynapseClientConfig) {
    this.config = config;
  }

  public async request(requestConfig: HttpRequest): Promise<HttpResponse> {
    const { method, url, headers = {}, params, data, timeout } = requestConfig;
    
    // Build full URL
    const fullUrl = url.startsWith('http') ? url : `${this.config.baseUrl}${url}`;
    const urlWithParams = this.buildUrlWithParams(fullUrl, params);

    // Prepare headers
    const requestHeaders = {
      'Content-Type': 'application/json',
      ...this.config.headers,
      ...headers,
    };

    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method,
      headers: requestHeaders,
      signal: timeout ? AbortSignal.timeout(timeout) : undefined,
    };

    if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
      fetchOptions.body = JSON.stringify(data);
    }

    // Execute request with retries
    return this.executeWithRetry(async () => {
      const response = await fetch(urlWithParams, fetchOptions);
      
      let responseData: unknown;
      const contentType = response.headers.get('content-type');
      
      if (contentType?.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      return {
        status: response.status,
        statusText: response.statusText,
        headers: this.headersToObject(response.headers),
        data: responseData,
        config: requestConfig,
      } as HttpResponse;
    });
  }

  public updateConfig(config: SynapseClientConfig): void {
    this.config = config;
  }

  private async executeWithRetry<T>(fn: () => Promise<T>): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= this.config.retries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.retries) {
          const delay = this.config.retryDelay * Math.pow(2, attempt); // Exponential backoff
          await this.sleep(delay);
        }
      }
    }
    
    throw lastError!;
  }

  private buildUrlWithParams(url: string, params?: Record<string, unknown>): string {
    if (!params || Object.keys(params).length === 0) {
      return url;
    }

    const urlObject = new URL(url);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlObject.searchParams.append(key, String(value));
      }
    });

    return urlObject.toString();
  }

  private headersToObject(headers: Headers): Record<string, string> {
    const headerObj: Record<string, string> = {};
    headers.forEach((value, key) => {
      headerObj[key] = value;
    });
    return headerObj;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
